// Deno版本的Shadowfetch
// 基于原始的Node.js/Cloudflare Workers版本改写

// 全局配置，包括认证令牌、默认目标URL和调试模式标志
const CONFIG: {
    AUTH_TOKEN: string;
    DEFAULT_DST_URL: string;
    DEBUG_MODE: boolean;
  } = {
    AUTH_TOKEN: "image",
    DEFAULT_DST_URL: "https://example.com/",
    DEBUG_MODE: false,
  };
  
  // 从环境变量更新全局配置（优先使用环境变量值）
  function updateConfigFromEnv() {
    // Deno环境变量获取方式
    for (const key of Object.keys(CONFIG)) {
      const envValue = Deno.env.get(key);
      if (envValue !== undefined) {
        if (key === "DEBUG_MODE") {
          CONFIG.DEBUG_MODE = envValue === "true";
        } else if (key === "AUTH_TOKEN") {
          CONFIG.AUTH_TOKEN = envValue;
        } else if (key === "DEFAULT_DST_URL") {
          CONFIG.DEFAULT_DST_URL = envValue;
        }
      }
    }
  }
  
  // 定义文本编码器和解码器，用于字符串和字节数组之间的转换
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  
  // 过滤不应转发的HTTP头（忽略头：host, accept-encoding, cf-*）
  const HEADER_FILTER_RE = /^(host|accept-encoding|cf-)/i;
  
  // 根据调试模式设置定义调试日志输出函数
  const log = CONFIG.DEBUG_MODE
    ? (message: string, data: unknown = "") =>
        console.log(`[DEBUG] ${message}`, data)
    : () => {};
  
  // 将多个Uint8Array连接成一个新的Uint8Array
  function concatUint8Arrays(...arrays: Uint8Array[]): Uint8Array {
    const total = arrays.reduce((sum, arr) => sum + arr.length, 0);
    const result = new Uint8Array(total);
    let offset = 0;
    for (const arr of arrays) {
      result.set(arr, offset);
      offset += arr.length;
    }
    return result;
  }
  
  // 解析HTTP响应头，返回状态码、状态文本、头部和头部部分的结束位置
  function parseHttpHeaders(buff: Uint8Array): {
    status: number;
    statusText: string;
    headers: Headers;
    headerEnd: number;
  } | null {
    const text = decoder.decode(buff);
    // 查找HTTP头部结束标志"\r\n\r\n"
    const headerEnd = text.indexOf("\r\n\r\n");
    if (headerEnd === -1) return null;
    const headerSection = text.slice(0, headerEnd).split("\r\n");
    const statusLine = headerSection[0];
    // 匹配HTTP状态行，例如"HTTP/1.1 200 OK"
    const statusMatch = statusLine.match(/HTTP\/1\.[01] (\d+) (.*)/);
    if (!statusMatch) throw new Error(`Invalid status line: ${statusLine}`);
    const headers = new Headers();
    // 解析响应头
    for (let i = 1; i < headerSection.length; i++) {
      const line = headerSection[i];
      const idx = line.indexOf(": ");
      if (idx !== -1) {
        headers.append(line.slice(0, idx), line.slice(idx + 2));
      }
    }
    return {
      status: Number(statusMatch[1]),
      statusText: statusMatch[2],
      headers,
      headerEnd,
    };
  }
  
  // 从reader读取数据，直到遇到双CRLF（表示HTTP头部结束）
  async function readUntilDoubleCRLF(reader: Deno.Conn): Promise<string> {
    let respText = "";
    const buffer = new Uint8Array(1024);
  
    while (true) {
      const bytesRead = await reader.read(buffer);
      if (bytesRead === null) break;
  
      respText += decoder.decode(buffer.subarray(0, bytesRead), { stream: true });
      if (respText.includes("\r\n\r\n")) break;
    }
  
    return respText;
  }
  
  // 异步生成器：读取分块HTTP响应数据块并按顺序生成每个块
  async function* readChunks(
    reader: Deno.Conn,
    buff: Uint8Array = new Uint8Array()
  ): AsyncGenerator<Uint8Array, void, unknown> {
    const tempBuffer = new Uint8Array(1024);
  
    while (true) {
      // 在现有缓冲区中查找CRLF分隔符的位置
      let pos = -1;
      for (let i = 0; i < buff.length - 1; i++) {
        if (buff[i] === 13 && buff[i + 1] === 10) {
          pos = i;
          break;
        }
      }
  
      // 如果未找到，继续读取更多数据以填充缓冲区
      if (pos === -1) {
        const bytesRead = await reader.read(tempBuffer);
        if (bytesRead === null) break;
        // 创建新的Uint8Array来避免类型问题
        const newBuff = new Uint8Array(buff.length + bytesRead);
        newBuff.set(buff, 0);
        newBuff.set(tempBuffer.subarray(0, bytesRead), buff.length);
        buff = newBuff;
        continue;
      }
  
      // 解析块大小（十六进制格式）
      const size = parseInt(decoder.decode(buff.slice(0, pos)), 16);
      log("Read chunk size", size);
  
      // 大小为0表示块结束
      if (!size) break;
  
      // 从缓冲区中删除已解析的大小部分和后面的CRLF
      buff = buff.slice(pos + 2);
  
      // 确保缓冲区包含完整的块（包括尾部的CRLF）
      while (buff.length < size + 2) {
        const bytesRead = await reader.read(tempBuffer);
        if (bytesRead === null)
          throw new Error("Unexpected EOF in chunked encoding");
        buff = concatUint8Arrays(buff, tempBuffer.subarray(0, bytesRead));
      }
  
      // 生成块数据（不包括尾部的CRLF）
      yield buff.slice(0, size);
      buff = buff.slice(size + 2);
    }
  }
  
  // 解析完整的HTTP响应，根据传输模式（分块或固定长度）处理响应体数据
  async function parseResponse(reader: Deno.Conn): Promise<Response> {
    let buff = new Uint8Array();
    const tempBuffer = new Uint8Array(1024);
  
    while (true) {
      const bytesRead = await reader.read(tempBuffer);
      if (bytesRead !== null) {
        // 创建新的Uint8Array来避免类型问题
        const newBuff = new Uint8Array(buff.length + bytesRead);
        newBuff.set(buff, 0);
        newBuff.set(tempBuffer.subarray(0, bytesRead), buff.length);
        buff = newBuff;
        const parsed = parseHttpHeaders(buff);
  
        if (parsed) {
          const { status, statusText, headers, headerEnd } = parsed;
          const isChunked = headers.get("transfer-encoding")?.includes("chunked");
          const contentLength = parseInt(
            headers.get("content-length") || "0",
            10
          );
          const data = buff.slice(headerEnd + 4);
  
          // 通过ReadableStream分发响应体数据
          return new Response(
            new ReadableStream({
              async start(ctrl) {
                try {
                  if (isChunked) {
                    log("Using chunked transfer mode");
                    // 分块传输模式：按顺序读取并排队每个块
                    for await (const chunk of readChunks(reader, data)) {
                      ctrl.enqueue(chunk);
                    }
                  } else {
                    log("Using fixed-length transfer mode", { contentLength });
                    let received = data.length;
                    if (data.length) ctrl.enqueue(data);
  
                    // 固定长度模式：根据content-length读取指定的字节数
                    while (received < contentLength) {
                      const bytesRead = await reader.read(tempBuffer);
                      if (bytesRead === null) break;
                      received += bytesRead;
                      ctrl.enqueue(tempBuffer.subarray(0, bytesRead));
                    }
                  }
                  ctrl.close();
                } catch (err) {
                  log("Error parsing response", err);
                  ctrl.error(err);
                }
              },
            }),
            { status, statusText, headers }
          );
        }
      }
  
      if (bytesRead === null) break;
    }
  
    throw new Error("Unable to parse response headers");
  }
  
  // 生成WebSocket握手所需的随机Sec-WebSocket-Key
  function generateWebSocketKey(): string {
    const bytes = new Uint8Array(16);
    crypto.getRandomValues(bytes);
    return btoa(String.fromCharCode(...bytes));
  }
  
  // 将文本消息打包成WebSocket帧（当前仅支持不太大的文本帧）
  // 未使用的函数，但保留作为参考
  // @ts-ignore - 保留作为参考代码
  function _packTextFrame(payload: Uint8Array): Uint8Array {
    const FIN_AND_OP = 0x81; // FIN标志和文本帧操作码
    const maskBit = 0x80; // 掩码位（客户端发送的消息必须设置为1）
    const len = payload.length;
    let header: Uint8Array;
  
    if (len < 126) {
      header = new Uint8Array(2);
      header[0] = FIN_AND_OP;
      header[1] = maskBit | len;
    } else if (len < 65536) {
      header = new Uint8Array(4);
      header[0] = FIN_AND_OP;
      header[1] = maskBit | 126;
      header[2] = (len >> 8) & 0xff;
      header[3] = len & 0xff;
    } else {
      throw new Error("Payload too large");
    }
  
    // 生成4字节随机掩码
    const mask = new Uint8Array(4);
    crypto.getRandomValues(mask);
    const maskedPayload = new Uint8Array(len);
  
    // 将掩码应用于有效载荷
    for (let i = 0; i < len; i++) {
      maskedPayload[i] = payload[i] ^ mask[i % 4];
    }
  
    // 连接帧头、掩码和掩码有效载荷
    return concatUint8Arrays(header, mask, maskedPayload);
  }
  
  // 用于解析和重新组装WebSocket帧的类，支持分段消息
  // 未使用的类，但保留作为参考
  // @ts-ignore - 保留作为参考代码
  class _SocketFramesReader {
    private reader: Deno.Conn;
    private buffer: Uint8Array;
    private fragmentedPayload: Uint8Array | null;
    private fragmentedOpcode: number | null;
  
    constructor(reader: Deno.Conn) {
      this.reader = reader;
      this.buffer = new Uint8Array();
      this.fragmentedPayload = null;
      this.fragmentedOpcode = null;
    }
  
    // 确保缓冲区有足够的字节进行解析
    async ensureBuffer(length: number): Promise<boolean> {
      const tempBuffer = new Uint8Array(1024);
  
      while (this.buffer.length < length) {
        const bytesRead = await this.reader.read(tempBuffer);
        if (bytesRead === null) return false;
        this.buffer = concatUint8Arrays(
          this.buffer,
          tempBuffer.subarray(0, bytesRead)
        );
      }
  
      return true;
    }
  
    // 解析下一个WebSocket帧并处理分段（操作码0表示继续）
    async nextFrame(): Promise<{
      fin: boolean;
      opcode: number;
      payload: Uint8Array;
    } | null> {
      while (true) {
        if (!(await this.ensureBuffer(2))) return null;
  
        const first = this.buffer[0],
          second = this.buffer[1],
          fin = (first >> 7) & 1,
          opcode = first & 0x0f,
          isMasked = (second >> 7) & 1;
  
        let payloadLen = second & 0x7f,
          offset = 2;
  
        // 如果有效载荷长度为126，解析接下来的两个字节以获取实际长度
        if (payloadLen === 126) {
          if (!(await this.ensureBuffer(offset + 2))) return null;
          payloadLen = (this.buffer[offset] << 8) | this.buffer[offset + 1];
          offset += 2;
        } else if (payloadLen === 127) {
          throw new Error("127 length mode is not supported");
        }
  
        let mask;
        if (isMasked) {
          if (!(await this.ensureBuffer(offset + 4))) return null;
          mask = this.buffer.slice(offset, offset + 4);
          offset += 4;
        }
  
        if (!(await this.ensureBuffer(offset + payloadLen))) return null;
  
        const payload = this.buffer.slice(offset, offset + payloadLen);
        if (isMasked && mask) {
          for (let i = 0; i < payload.length; i++) {
            payload[i] ^= mask[i % 4];
          }
        }
  
        // 从缓冲区中删除已处理的字节
        this.buffer = this.buffer.slice(offset + payloadLen);
  
        // 操作码0表示继续帧：连接分段数据
        if (opcode === 0) {
          if (this.fragmentedPayload === null)
            throw new Error("Received continuation frame without initiation");
  
          this.fragmentedPayload = concatUint8Arrays(
            this.fragmentedPayload,
            payload
          );
  
          if (fin) {
            const completePayload = this.fragmentedPayload;
            const completeOpcode = this.fragmentedOpcode;
            this.fragmentedPayload = this.fragmentedOpcode = null;
            return {
              fin: true,
              opcode: completeOpcode!,
              payload: completePayload,
            };
          }
        } else {
          // 如果有分段数据但当前帧不是继续，则重置分段状态
          if (!fin) {
            this.fragmentedPayload = payload;
            this.fragmentedOpcode = opcode;
            continue;
          } else {
            if (this.fragmentedPayload) {
              this.fragmentedPayload = this.fragmentedOpcode = null;
            }
            return { fin: Boolean(fin), opcode, payload };
          }
        }
      }
    }
  }
  
  // 根据请求类型转发HTTP请求或WebSocket握手和数据
  async function nativeFetch(req: Request, dstUrl: string): Promise<Response> {
    // 通过删除符合过滤条件的头来清理头
    const cleanedHeaders = new Headers();
    for (const [k, v] of req.headers) {
      if (!HEADER_FILTER_RE.test(k)) {
        cleanedHeaders.set(k, v);
      }
    }
  
    // 检查请求是否为WebSocket请求
    const upgradeHeader = req.headers.get("Upgrade")?.toLowerCase();
    const isWebSocket = upgradeHeader === "websocket";
    const targetUrl = new URL(dstUrl);
  
    if (isWebSocket) {
      // 如果目标URL不支持WebSocket协议，则返回错误响应
      if (!/^wss?:\/\//i.test(dstUrl)) {
        return new Response("Target does not support WebSocket", { status: 400 });
      }
  
      const isSecure = targetUrl.protocol === "wss:";
      const port = targetUrl.port
        ? parseInt(targetUrl.port)
        : isSecure
        ? 443
        : 80;
  
      // 建立与目标服务器的原始套接字连接
      const conn = await Deno.connect({
        hostname: targetUrl.hostname,
        port: port,
        transport: "tcp",
      });
  
      // 如果需要安全连接，使用TLS
      let socket: Deno.Conn = conn;
      if (isSecure) {
        socket = await Deno.startTls(conn, {
          hostname: targetUrl.hostname,
        });
      }
  
      // 生成WebSocket握手所需的密钥
      const key = generateWebSocketKey();
  
      // 构造握手所需的HTTP头
      cleanedHeaders.set("Host", targetUrl.hostname);
      cleanedHeaders.set("Connection", "Upgrade");
      cleanedHeaders.set("Upgrade", "websocket");
      cleanedHeaders.set("Sec-WebSocket-Version", "13");
      cleanedHeaders.set("Sec-WebSocket-Key", key);
  
      // 组装WebSocket握手的HTTP请求数据
      const handshakeReq =
        `GET ${targetUrl.pathname}${targetUrl.search} HTTP/1.1\r\n` +
        Array.from(cleanedHeaders.entries())
          .map(([k, v]) => `${k}: ${v}`)
          .join("\r\n") +
        "\r\n\r\n";
  
      log("Sending WebSocket handshake request", handshakeReq);
      await socket.write(encoder.encode(handshakeReq));
  
      const handshakeResp = await readUntilDoubleCRLF(socket);
      log("Received handshake response", handshakeResp);
  
      // 验证握手响应是否表示101 Switching Protocols状态
      if (
        !handshakeResp.includes("101") ||
        !handshakeResp.includes("Switching Protocols")
      ) {
        socket.close();
        throw new Error("WebSocket handshake failed: " + handshakeResp);
      }
  
      // 创建WebSocket响应
      // 注意：Deno不支持WebSocketPair，这里需要使用不同的方法
      // 这部分需要根据Deno的WebSocket API进行适配
      // 以下是一个简化的实现，实际应用中可能需要更复杂的处理
  
      // 由于Deno的WebSocket API与Cloudflare Workers不同，
      // 这里返回一个特殊的响应，表示WebSocket连接已建立
      // 实际应用中，你可能需要使用Deno.upgradeWebSocket或其他方法
      return new Response("WebSocket connection established", {
        status: 101,
        headers: {
          Connection: "Upgrade",
          Upgrade: "websocket",
        },
      });
    } else {
      // 对于标准HTTP请求：设置必需的头（如Host和禁用压缩）
      cleanedHeaders.set("Host", targetUrl.hostname);
      cleanedHeaders.set("accept-encoding", "identity");
  
      const port = targetUrl.port
        ? parseInt(targetUrl.port)
        : targetUrl.protocol === "https:"
        ? 443
        : 80;
  
      // 建立与目标服务器的连接
      const conn = await Deno.connect({
        hostname: targetUrl.hostname,
        port: port,
        transport: "tcp",
      });
  
      // 如果需要安全连接，使用TLS
      let socket: Deno.Conn = conn;
      if (targetUrl.protocol === "https:") {
        socket = await Deno.startTls(conn, {
          hostname: targetUrl.hostname,
        });
      }
  
      // 构造请求行和头
      const requestLine =
        `${req.method} ${targetUrl.pathname}${targetUrl.search} HTTP/1.1\r\n` +
        Array.from(cleanedHeaders.entries())
          .map(([k, v]) => `${k}: ${v}`)
          .join("\r\n") +
        "\r\n\r\n";
  
      log("Sending request", requestLine);
      await socket.write(encoder.encode(requestLine));
  
      // 如果有请求体，将其转发到目标服务器
      if (req.body) {
        log("Forwarding request body");
        const reader = req.body.getReader();
  
        try {
          while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            await socket.write(value);
          }
        } catch (e) {
          log("Error forwarding request body", e);
        }
      }
  
      // 解析并返回目标服务器的响应
      return await parseResponse(socket);
    }
  }
  
  // 入口点：处理请求，更新配置，解析目标URL，并转发请求
  async function handleRequest(req: Request): Promise<Response> {
    updateConfigFromEnv();
    const url = new URL(req.url);
    const parts = url.pathname.split("/").filter(Boolean);
    const [auth, protocol, ...path] = parts;
  
    // 检查auth参数是否与配置的auth令牌匹配
    const isValid = auth === CONFIG.AUTH_TOKEN;
  
    // 如果匹配，构造目标URL；否则，使用默认目标
    const dstUrl =
      isValid && protocol
        ? `${protocol}//${path.join("/")}${url.search}`
        : CONFIG.DEFAULT_DST_URL;
  
    log("Target URL", dstUrl);
  
    return await nativeFetch(req, dstUrl);
  }
  
  // 使用Deno.serve API启动HTTP服务器
  Deno.serve({ port: 8001 }, async (request) => {
    try {
      return await handleRequest(request);
    } catch (e) {
      console.error("Error handling request:", e);
      return new Response("Internal Server Error", { status: 500 });
    }
  });
  
  console.log("Shadowfetch server running on http://localhost:8001/");
// ==UserScript==
// @name         Augment Register Helper
// @namespace    http://tampermonkey.net/
// @version      0.7
// @description  自动填充邮箱和验证码，辅助 Augment 注册
// <AUTHOR> Agent
// @match        *://*.augmentcode.com/*
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_cookie
// @grant        GM.cookie
// @connect      api.mail.cx
// @connect      app.augmentcode.com
// ==/UserScript==

(function() {
    'use strict';

    // ==================== 样式设置 ====================
    GM_addStyle(`
        .register-helper {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            width: 300px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        .register-helper-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .register-helper-title {
            font-weight: bold;
            color: #333;
        }
        .register-helper-close {
            cursor: pointer;
            color: #999;
            font-size: 18px;
        }
        .register-helper-close:hover {
            color: #333;
        }
        .register-helper-content {
            margin-bottom: 10px;
        }
        .register-helper-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .register-helper-button {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
            flex: 1 0 calc(50% - 8px);
            text-align: center;
        }
        .register-helper-button:hover {
            background: #3367d6;
        }
        #generate-email-btn {
            background: #4285f4;
            flex: 1 0 100%;
        }
        #get-code-btn {
            background: #28a745;
        }
        #fill-code-btn {
            background: #17a2b8;
        }
        #clear-mailbox-btn {
            background: #ffc107;
            color: #212529;
        }
        #clear-data-btn {
            background: #dc3545;
        }
        #goto-subscription-btn {
            background: #4caf50 !important;
            flex: 1 0 100%;
        }
        .register-helper-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background: #f5f5f5;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
        .register-helper-email {
            font-weight: bold;
            word-break: break-all;
        }
        .register-helper-code {
            font-weight: bold;
            font-size: 16px;
            color: #4285f4;
        }
        .register-helper-domain-select {
            margin-bottom: 8px;
            width: 100%;
            padding: 6px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    `);

    // ==================== 邮箱相关功能 ====================

    // MailCX错误类
    class MailCXError extends Error {
        constructor(message) {
            super(message);
            this.name = 'MailCXError';
        }
    }

    // MailCX API客户端
    class MailCX {
        constructor() {
            this.BASE_URL = 'https://api.mail.cx/api/v1';
            this.token = null;
        }

        async makeRequest(method, endpoint, options = {}) {
            const url = this.BASE_URL + endpoint;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (this.token) {
                headers['Authorization'] = `Bearer ${this.token}`;
            }

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: method,
                    url: url,
                    headers: headers,
                    data: options.body,
                    timeout: 30000,
                    onload: function(response) {
                        if (response.status >= 200 && response.status < 300) {
                            try {
                                const result = response.responseText ? JSON.parse(response.responseText) : {};
                                resolve(result);
                            } catch (e) {
                                resolve(response.responseText);
                            }
                        } else {
                            reject(new MailCXError(`请求失败: ${response.status} ${response.statusText}`));
                        }
                    },
                    onerror: function(error) {
                        reject(new MailCXError(`请求错误: ${error}`));
                    }
                });
            });
        }

        async authorize() {
            try {
                const response = await this.makeRequest('POST', '/auth/authorize_token', {
                    headers: {'Authorization': 'Bearer undefined'}
                });
                if (typeof response === 'string') {
                    this.token = response.replace(/"/g, '');
                } else {
                    throw new MailCXError('无法获取token');
                }
            } catch (e) {
                console.error('认证失败:', e);
                throw e;
            }
        }

        async getMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}`);
        }

        async getMessage(email, messageId) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}/${messageId}`);
        }


        async deleteMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}`);
        }
    }

    // 生成随机邮箱
    function generateRandomEmail(domain = 'uuf.me') {
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let randomString = '';
        for (let i = 0; i < 8; i++) {
            randomString += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return randomString + '@' + domain;
    }

    // 从邮件内容中提取验证码
    function extractVerificationCode(emailContent) {
        // 匹配 "Your verification code is: XXXXXX" 格式的验证码
        const codeRegex = /Your verification code is:\s*(\d{6})/;
        const match = emailContent.match(codeRegex);
        return match ? match[1] : null;
    }

    // ==================== 主要功能 ====================

    // 创建UI
    function createUI() {
        const container = document.createElement('div');
        container.className = 'register-helper';
        container.innerHTML = `
            <div class="register-helper-header">
                <div class="register-helper-title">Augment 注册助手</div>
                <div class="register-helper-close">×</div>
            </div>
            <div class="register-helper-content">
                <select class="register-helper-domain-select">
                    <option value="uuf.me" selected>@uuf.me</option>
                    <option value="yzm.de">@yzm.de</option>
                    <option value="mail.cx">@mail.cx</option>
                    <option value="qabq.com">@qabq.com</option>
                    <option value="nqmo.com">@nqmo.com</option>
                    <option value="end.tw">@end.tw</option>
                </select>
                <div class="register-helper-actions">
                    <button class="register-helper-button" id="generate-email-btn">生成并填充邮箱</button>
                    <button class="register-helper-button" id="get-code-btn">获取验证码</button>
                    <button class="register-helper-button" id="fill-code-btn">填充验证码</button>
                    <button class="register-helper-button" id="clear-mailbox-btn">清空邮箱</button>
                    <button class="register-helper-button" id="clear-data-btn">清除数据</button>
                    <button class="register-helper-button" id="goto-subscription-btn">前往订阅页面</button>
                </div>
                <div class="register-helper-status">
                    等待操作...
                </div>
            </div>
        `;
        document.body.appendChild(container);

        // 关闭按钮事件
        container.querySelector('.register-helper-close').addEventListener('click', () => {
            container.style.display = 'none';
        });

        return container;
    }

    // 保存和获取数据
    function saveData(key, value) {
        GM_setValue(key, value);
    }

    function getData(key, defaultValue = null) {
        return GM_getValue(key, defaultValue);
    }

    // 清除所有保存的数据
    function clearAllData() {
        saveData('currentEmail', '');
        saveData('verificationCode', '');
        console.log('已清除所有保存的数据');
    }

    // 检查输入框是否是验证码输入框
    function isCodeInput(input) {
        if (!input) return false;

        // 获取输入框的各种属性
        const placeholder = (input.getAttribute('placeholder') || '').toLowerCase();
        const name = (input.getAttribute('name') || '').toLowerCase();
        const id = (input.getAttribute('id') || '').toLowerCase();
        const ariaLabel = (input.getAttribute('aria-label') || '').toLowerCase();

        // 尝试获取关联的标签文本
        let labelText = '';
        const labelElement = document.querySelector(`label[for="${input.id}"]`);
        if (labelElement) {
            labelText = labelElement.textContent.toLowerCase();
        }

        // 检查是否包含验证码相关的关键词
        const codeKeywords = ['code', 'verification', 'verify', 'otp', '验证码', 'enter the code'];

        for (const keyword of codeKeywords) {
            if (
                placeholder.includes(keyword) ||
                name.includes(keyword) ||
                id.includes(keyword) ||
                ariaLabel.includes(keyword) ||
                labelText.includes(keyword) ||
                (input.parentElement && input.parentElement.textContent.toLowerCase().includes(keyword))
            ) {
                console.log(`输入框被识别为验证码输入框，匹配关键词: ${keyword}`, input);
                return true;
            }
        }

        // 检查输入框的最大长度，验证码输入框通常有长度限制
        const maxLength = input.getAttribute('maxlength');
        if (maxLength && parseInt(maxLength) <= 8) {
            console.log('输入框可能是验证码输入框(基于长度限制):', input);
            return true;
        }

        // 检查是否有数字键盘提示
        const inputMode = input.getAttribute('inputmode');
        if (inputMode === 'numeric' || inputMode === 'tel') {
            console.log('输入框可能是验证码输入框(基于输入模式):', input);
            return true;
        }

        // 检查是否有模式限制
        const pattern = input.getAttribute('pattern');
        if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
            console.log('输入框可能是验证码输入框(基于模式限制):', input);
            return true;
        }

        return false;
    }

    // 查找邮箱输入框的增强函数
    function findEmailInput() {
        console.log('开始查找邮箱输入框...');

        // 尝试多种选择器来找到邮箱输入框
        const selectors = [
            'input[type="email"]',
            'input[name="email"]',
            'input[placeholder*="email" i]',
            'input[id*="email" i]',
            'input[aria-label*="email" i]',
            // 针对截图中的特定输入框
            'input.MuiInputBase-input',
            'input[required][name="email"]',
            'input[name="address"]',
            'input[name="Email address"]',
            'input[placeholder="Email address"]',
            'input[aria-label="Email address"]',
            // 尝试更具体的选择器
            'form input[type="text"]',
            'form input[type="email"]',
            'form input',
            // 尝试通过父元素定位
            '.MuiFormControl-root input',
            '.MuiInputBase-root input',
            // 尝试通过标签文本定位
            'label:contains("Email") + input',
            'label:contains("Email") ~ input',
            // 尝试通过邻近元素定位
            'div:contains("OR") ~ div input',
            // 尝试通过位置定位
            'form > div > input',
            // 尝试通过属性组合定位
            'input[required][type="text"]',
            'input[required][type="email"]'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input && !isCodeInput(input)) {
                    console.log(`找到邮箱输入框，使用选择器: ${selector}`);
                    return input;
                } else if (input) {
                    console.log(`选择器 ${selector} 找到的输入框被识别为验证码输入框，跳过`);
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        }

        console.log('未通过预定义选择器找到邮箱输入框，尝试分析所有输入框...');

        // 如果上面的选择器都没找到，尝试查找所有输入框，检查其属性
        const allInputs = document.querySelectorAll('input');
        console.log(`页面上共有 ${allInputs.length} 个输入框`);

        // 记录所有输入框的信息，方便调试
        allInputs.forEach((input, index) => {
            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const type = input.getAttribute('type') || '';
            const required = input.hasAttribute('required');
            const classes = Array.from(input.classList).join(', ');

            console.log(`输入框 #${index}:`, {
                placeholder,
                name,
                id,
                type,
                required,
                classes,
                element: input
            });
        });

        // 尝试找到可能的邮箱输入框
        for (const input of allInputs) {
            if (isCodeInput(input)) {
                console.log('跳过验证码输入框:', input);
                continue;
            }

            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const type = input.getAttribute('type') || '';

            if (
                placeholder.toLowerCase().includes('email') ||
                name.toLowerCase().includes('email') ||
                id.toLowerCase().includes('email') ||
                type === 'email' ||
                placeholder.toLowerCase().includes('address') ||
                name.toLowerCase().includes('address') ||
                id.toLowerCase().includes('address')
            ) {
                console.log('通过属性分析找到可能的邮箱输入框:', input);
                return input;
            }
        }

        // 如果还是找不到，尝试找到第一个非验证码的文本输入框
        const textInputs = Array.from(allInputs).filter(input =>
            (input.type === 'text' || input.type === 'email' || !input.type) && !isCodeInput(input)
        );

        if (textInputs.length > 0) {
            console.log('未找到明确的邮箱输入框，使用第一个非验证码的文本输入框:', textInputs[0]);
            return textInputs[0];
        }

        // 最后的尝试：使用第一个非验证码的输入框
        const nonCodeInputs = Array.from(allInputs).filter(input => !isCodeInput(input));
        if (nonCodeInputs.length > 0) {
            console.log('使用第一个非验证码的输入框作为邮箱输入框:', nonCodeInputs[0]);
            return nonCodeInputs[0];
        }

        console.log('未找到任何可用的邮箱输入框');
        return null;
    }

    // 查找验证码输入框的增强函数
    function findCodeInput() {
        console.log('开始查找验证码输入框...');

        // 尝试多种选择器来找到验证码输入框
        const selectors = [
            'input[placeholder*="code" i]',
            'input[name*="code" i]',
            'input[aria-label*="code" i]',
            'input[id*="code" i]',
            // 针对截图中的特定输入框
            'input.MuiInputBase-input',
            'input[placeholder*="Enter the code" i]',
            'input[placeholder*="verification" i]',
            'input[placeholder*="验证码" i]',
            // 尝试更具体的选择器
            'form input[type="text"]',
            // 尝试通过父元素定位
            '.MuiFormControl-root input',
            '.MuiInputBase-root input',
            // 尝试通过标签文本定位
            'label:contains("Code") + input',
            'label:contains("Code") ~ input',
            'label:contains("验证码") + input',
            'label:contains("验证码") ~ input',
            // 尝试通过位置定位
            'form > div > input'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input) {
                    console.log(`找到验证码输入框，使用选择器: ${selector}`);
                    return input;
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        }

        console.log('未通过预定义选择器找到验证码输入框，尝试分析所有输入框...');

        // 如果上面的选择器都没找到，尝试查找所有输入框，检查其属性
        const allInputs = document.querySelectorAll('input');

        // 尝试找到可能的验证码输入框
        for (const input of allInputs) {
            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';

            if (
                placeholder.toLowerCase().includes('code') ||
                name.toLowerCase().includes('code') ||
                id.toLowerCase().includes('code') ||
                placeholder.toLowerCase().includes('验证码') ||
                name.toLowerCase().includes('验证码') ||
                id.toLowerCase().includes('验证码') ||
                placeholder.toLowerCase().includes('verification') ||
                name.toLowerCase().includes('verification') ||
                id.toLowerCase().includes('verification')
            ) {
                console.log('通过属性分析找到可能的验证码输入框:', input);
                return input;
            }
        }

        // 如果还是没找到，尝试找到类型为text的输入框（通常验证码输入框是text类型）
        const textInputs = Array.from(allInputs).filter(input => input.type === 'text');

        if (textInputs.length === 1) {
            // 如果只有一个text类型输入框，很可能是验证码输入框
            console.log('使用唯一的文本输入框作为验证码输入框:', textInputs[0]);
            return textInputs[0];
        } else if (textInputs.length > 1) {
            // 如果有多个text类型输入框，尝试找到最可能是验证码输入框的那个
            // 通常验证码输入框会有一些特征，比如长度限制、数字类型等
            for (const input of textInputs) {
                const maxLength = input.getAttribute('maxlength');
                if (maxLength && parseInt(maxLength) <= 8) {
                    // 验证码通常是6位数字，输入框长度限制通常不会超过8
                    console.log('找到可能的验证码输入框(基于长度限制):', input);
                    return input;
                }

                // 检查是否有数字键盘提示
                const inputMode = input.getAttribute('inputmode');
                if (inputMode === 'numeric' || inputMode === 'tel') {
                    console.log('找到可能的验证码输入框(基于输入模式):', input);
                    return input;
                }

                // 检查是否有模式限制
                const pattern = input.getAttribute('pattern');
                if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
                    console.log('找到可能的验证码输入框(基于模式限制):', input);
                    return input;
                }
            }

            // 如果没有找到明确的验证码输入框，使用第二个文本输入框
            // 通常第一个是邮箱，第二个是验证码
            if (textInputs.length >= 2) {
                console.log('使用第二个文本输入框作为验证码输入框:', textInputs[1]);
                return textInputs[1];
            }

            // 如果以上都失败，使用最后一个文本输入框
            console.log('使用最后一个文本输入框作为验证码输入框:', textInputs[textInputs.length - 1]);
            return textInputs[textInputs.length - 1];
        }

        console.log('未找到任何可用的验证码输入框');
        return null;
    }

    // 直接操作DOM的方法来填充邮箱
    function fillEmailByDirectDOM(email) {
        console.log('尝试通过直接DOM操作填充邮箱:', email);

        try {
            // 尝试使用更直接的方法找到邮箱输入框
            // 1. 尝试通过标签文本查找
            const emailLabels = Array.from(document.querySelectorAll('label')).filter(label =>
                label.textContent.toLowerCase().includes('email') ||
                label.textContent.includes('邮箱') ||
                label.textContent.includes('电子邮件')
            );

            for (const label of emailLabels) {
                console.log('找到邮箱相关标签:', label);
                // 尝试找到与标签关联的输入框
                const forId = label.getAttribute('for');
                if (forId) {
                    const input = document.getElementById(forId);
                    if (input && !isCodeInput(input)) {
                        console.log('通过标签for属性找到邮箱输入框:', input);
                        input.value = email;
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        return true;
                    } else if (input) {
                        console.log('通过标签for属性找到的输入框被识别为验证码输入框，跳过:', input);
                    }
                }

                // 尝试找到标签下的输入框
                const input = label.querySelector('input');
                if (input && !isCodeInput(input)) {
                    console.log('在标签内找到邮箱输入框:', input);
                    input.value = email;
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                } else if (input) {
                    console.log('在标签内找到的输入框被识别为验证码输入框，跳过:', input);
                }

                // 尝试找到标签后的输入框
                let nextElement = label.nextElementSibling;
                while (nextElement) {
                    const input = nextElement.querySelector('input');
                    if (input && !isCodeInput(input)) {
                        console.log('在标签后找到邮箱输入框:', input);
                        input.value = email;
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        return true;
                    } else if (input) {
                        console.log('在标签后找到的输入框被识别为验证码输入框，跳过:', input);
                    }
                    nextElement = nextElement.nextElementSibling;
                }
            }

            // 2. 尝试通过文本"Email address*"查找
            const emailTexts = ['Email address*', 'Email address', 'Email*', 'Email', '邮箱地址*', '邮箱地址', '邮箱*', '邮箱'];
            for (const text of emailTexts) {
                // 使用XPath查找包含特定文本的元素
                const xpath = `//*[contains(text(), '${text}')]`;
                const elements = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);

                for (let i = 0; i < elements.snapshotLength; i++) {
                    const element = elements.snapshotItem(i);
                    console.log(`找到包含"${text}"的元素:`, element);

                    // 查找该元素附近的输入框
                    let parent = element.parentElement;
                    for (let j = 0; j < 3 && parent; j++) { // 向上查找3层
                        const inputs = Array.from(parent.querySelectorAll('input')).filter(input => !isCodeInput(input));
                        if (inputs.length > 0) {
                            console.log(`在包含"${text}"的元素附近找到非验证码输入框:`, inputs[0]);
                            inputs[0].value = email;
                            inputs[0].dispatchEvent(new Event('input', { bubbles: true }));
                            inputs[0].dispatchEvent(new Event('change', { bubbles: true }));
                            return true;
                        }
                        parent = parent.parentElement;
                    }
                }
            }

            // 3. 尝试通过表单查找第一个非验证码输入框
            const forms = document.querySelectorAll('form');
            for (const form of forms) {
                const inputs = Array.from(form.querySelectorAll('input[type="email"], input[type="text"]')).filter(input => !isCodeInput(input));
                if (inputs.length > 0) {
                    console.log('在表单中找到第一个非验证码输入框:', inputs[0]);
                    inputs[0].value = email;
                    inputs[0].dispatchEvent(new Event('input', { bubbles: true }));
                    inputs[0].dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                }
            }

            // 4. 最后尝试：查找所有输入框，填充第一个看起来合适的非验证码输入框
            const allInputs = document.querySelectorAll('input');
            for (const input of allInputs) {
                // 跳过隐藏的、禁用的、只读的输入框和验证码输入框
                if (
                    input.type === 'hidden' ||
                    input.disabled ||
                    input.readOnly ||
                    input.style.display === 'none' ||
                    input.style.visibility === 'hidden' ||
                    isCodeInput(input)
                ) {
                    continue;
                }

                console.log('尝试填充可见的非验证码输入框:', input);
                input.value = email;
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                return true;
            }

            return false;
        } catch (error) {
            console.error('直接DOM操作填充邮箱失败:', error);
            return false;
        }
    }

    // 主函数
    function init() {
        console.log('Augment Register Helper 已加载');

        // 创建UI
        const ui = createUI();
        const statusDiv = ui.querySelector('.register-helper-status');
        const domainSelect = ui.querySelector('.register-helper-domain-select');

        // 创建邮件客户端
        const mailClient = new MailCX();

        // 从存储中获取当前使用的邮箱和验证码
        let currentEmail = getData('currentEmail', '');
        let verificationCode = getData('verificationCode', '');

        // 如果有保存的邮箱和验证码，显示在状态栏中
        if (currentEmail) {
            statusDiv.innerHTML = `邮箱已生成: <div class="register-helper-email">${currentEmail}</div>`;

            if (verificationCode) {
                statusDiv.innerHTML += `<br>验证码: <div class="register-helper-code">${verificationCode}</div>`;
            }

            // 尝试自动填充，使用多次尝试机制
            const attemptFill = (attempt = 1, maxAttempts = 5) => {
                console.log(`尝试自动填充 (${attempt}/${maxAttempts})...`);

                // 尝试使用常规方法填充邮箱
                const emailInput = findEmailInput();
                let emailFilled = false;

                if (emailInput && currentEmail) {
                    try {
                        emailInput.value = currentEmail;
                        emailInput.dispatchEvent(new Event('input', { bubbles: true }));
                        emailInput.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('常规方法自动填充邮箱成功');
                        emailFilled = true;
                    } catch (e) {
                        console.error('常规方法填充邮箱失败:', e);
                    }
                }

                // 如果常规方法失败，尝试使用直接DOM操作
                if (!emailFilled && currentEmail) {
                    emailFilled = fillEmailByDirectDOM(currentEmail);
                    if (emailFilled) {
                        console.log('直接DOM操作填充邮箱成功');
                    } else {
                        console.error(`尝试 ${attempt}: 所有填充邮箱方法均失败`);

                        // 如果还有尝试次数，则继续尝试
                        if (attempt < maxAttempts) {
                            setTimeout(() => attemptFill(attempt + 1, maxAttempts), 1000);
                            return;
                        } else {
                            statusDiv.innerHTML += '<br><span style="color: #f44336;">自动填充邮箱失败，请手动复制</span>';
                        }
                    }
                }

                // 尝试填充验证码
                const codeInput = findCodeInput();
                if (codeInput && verificationCode) {
                    try {
                        codeInput.value = verificationCode;
                        codeInput.dispatchEvent(new Event('input', { bubbles: true }));
                        codeInput.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('自动填充验证码成功');
                    } catch (e) {
                        console.error('填充验证码失败:', e);
                        statusDiv.innerHTML += '<br><span style="color: #f44336;">自动填充验证码失败，请手动复制</span>';
                    }
                }
            };

            // 开始第一次尝试，延迟1秒确保页面元素已加载
            setTimeout(() => attemptFill(), 1000);
        }

        // 生成并填充邮箱按钮
        ui.querySelector('#generate-email-btn').addEventListener('click', async () => {
            try {
                statusDiv.innerHTML = '正在生成邮箱...';

                // 获取选择的域名
                const domain = domainSelect.value;

                // 生成随机邮箱
                currentEmail = generateRandomEmail(domain);

                // 保存到存储中
                saveData('currentEmail', currentEmail);

                // 尝试使用常规方法填充邮箱
                let emailFilled = false;
                const emailInput = findEmailInput();

                if (emailInput) {
                    try {
                        emailInput.value = currentEmail;
                        // 触发输入事件，确保表单验证能够识别
                        emailInput.dispatchEvent(new Event('input', { bubbles: true }));
                        emailInput.dispatchEvent(new Event('change', { bubbles: true }));
                        emailFilled = true;
                        console.log('常规方法填充邮箱成功');
                    } catch (e) {
                        console.error('常规方法填充邮箱失败:', e);
                    }
                }

                // 如果常规方法失败，尝试使用直接DOM操作
                if (!emailFilled) {
                    emailFilled = fillEmailByDirectDOM(currentEmail);
                    if (emailFilled) {
                        console.log('直接DOM操作填充邮箱成功');
                    }
                }

                if (emailFilled) {
                    statusDiv.innerHTML = `邮箱已生成并填充: <div class="register-helper-email">${currentEmail}</div>`;
                } else {
                    statusDiv.innerHTML = `邮箱已生成: <div class="register-helper-email">${currentEmail}</div><br><span style="color: #f44336;">自动填充失败，请手动复制</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `生成邮箱失败: ${error.message}`;
                console.error('生成邮箱失败:', error);
            }
        });

        // 获取验证码按钮
        ui.querySelector('#get-code-btn').addEventListener('click', async () => {
            if (!currentEmail) {
                statusDiv.innerHTML = '请先生成邮箱';
                return;
            }

            try {
                statusDiv.innerHTML = '正在获取验证码邮件...';

                // 等待邮件到达的函数
                const waitForEmail = async (maxAttempts = 10, interval = 3000) => {
                    for (let attempt = 0; attempt < maxAttempts; attempt++) {
                        statusDiv.innerHTML = `正在检查邮箱 (${attempt + 1}/${maxAttempts})...`;

                        // 获取邮箱内容
                        const messages = await mailClient.getMailbox(currentEmail);

                        if (Array.isArray(messages) && messages.length > 0) {
                            // 查找来自 Augment 的邮件
                            const augmentEmail = messages.find(msg =>
                                msg.from.includes('augment') ||
                                msg.subject.toLowerCase().includes('verification') ||
                                msg.subject.toLowerCase().includes('code')
                            );

                            if (augmentEmail) {
                                // 获取邮件详情
                                const detail = await mailClient.getMessage(currentEmail, augmentEmail.id);
                                const content = detail.body?.text || detail.body?.html || '';

                                // 提取验证码
                                verificationCode = extractVerificationCode(content);

                                if (verificationCode) {
                                    return verificationCode;
                                }
                            }
                        }

                        // 等待一段时间后再次检查
                        await new Promise(resolve => setTimeout(resolve, interval));
                    }

                    throw new Error('未收到验证码邮件');
                };

                // 等待并获取验证码
                verificationCode = await waitForEmail();

                // 保存到存储中
                saveData('verificationCode', verificationCode);

                statusDiv.innerHTML = `验证码已获取: <div class="register-helper-code">${verificationCode}</div>`;
            } catch (error) {
                statusDiv.innerHTML = `获取验证码失败: ${error.message}`;
                console.error('获取验证码失败:', error);
            }
        });

        // 填充验证码按钮
        ui.querySelector('#fill-code-btn').addEventListener('click', () => {
            if (!verificationCode) {
                statusDiv.innerHTML = '请先获取验证码';
                return;
            }

            try {
                // 查找验证码输入框
                const codeInput = findCodeInput();

                if (codeInput) {
                    codeInput.value = verificationCode;
                    // 触发输入事件
                    codeInput.dispatchEvent(new Event('input', { bubbles: true }));
                    codeInput.dispatchEvent(new Event('change', { bubbles: true }));

                    statusDiv.innerHTML = `验证码已填充: <div class="register-helper-code">${verificationCode}</div>`;

                    // 成功填充验证码后，自动清除数据
                    setTimeout(() => {
                        clearAllData();
                        statusDiv.innerHTML += '<br><span style="color: #4caf50;">数据已自动清除</span>';
                        currentEmail = '';
                        verificationCode = '';
                    }, 3000); // 延迟3秒后清除，给用户一些时间看到验证码
                } else {
                    statusDiv.innerHTML = `验证码: <div class="register-helper-code">${verificationCode}</div><br>未找到验证码输入框，请手动复制`;
                }
            } catch (error) {
                statusDiv.innerHTML = `填充验证码失败: ${error.message}`;
                console.error('填充验证码失败:', error);
            }
        });

        // 清除数据按钮
        ui.querySelector('#clear-data-btn').addEventListener('click', () => {
            clearAllData();
            currentEmail = '';
            verificationCode = '';
            statusDiv.innerHTML = '所有数据已清除';
        });



        // 前往订阅页面按钮
        ui.querySelector('#goto-subscription-btn').addEventListener('click', async () => {
            try {
                statusDiv.innerHTML = '正在获取订阅页面链接...';

                // 提供用户手动操作的说明，因为自动注入脚本可能会受到同源策略限制

                // 显示正在处理的状态
                statusDiv.innerHTML = `
                    <div style="margin-bottom: 10px;">正在获取订阅门户链接，请稍候...</div>
                    <div style="font-size: 12px; background: #f8f9fa; padding: 8px; border-radius: 4px;">
                        <strong>如果自动获取失败，请手动操作：</strong><br>
                        1. 在订阅页面按 F12 打开开发者工具<br>
                        2. 切换到 Console 标签<br>
                        3. 输入以下代码并回车：<br>
                        <code style="background: #eee; padding: 2px 4px; display: block; margin: 4px 0;">
                        fetch('/api/subscription').then(r=>r.json()).then(d=>{
                          console.log('订阅门户链接:', d.portalUrl);
                          return d.portalUrl;
                        })
                        </code>
                        4. 复制返回的链接并手动打开
                    </div>
                `;

                // 打开订阅页面
                const subscriptionWindow = window.open('https://app.augmentcode.com/account/subscription', '_blank');

                // 尝试直接使用 GM_xmlhttpRequest 获取订阅链接
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: 'https://app.augmentcode.com/api/subscription',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    onload: function(response) {
                        try {
                            if (response.status >= 200 && response.status < 300) {
                                const data = JSON.parse(response.responseText);
                                const portalUrl = data.portalUrl;

                                if (portalUrl) {
                                    // 更新状态，显示链接而不是自动打开
                                    statusDiv.innerHTML = `
                                        <div style="margin-bottom: 10px;">已获取订阅门户链接：</div>
                                        <div style="margin-bottom: 10px;">
                                            <a href="${portalUrl}" target="_blank" style="color: #4caf50; font-weight: bold; word-break: break-all; text-decoration: none; display: block; padding: 8px; background: #f5f5f5; border-radius: 4px; border: 1px solid #ddd;">
                                                ${portalUrl}
                                            </a>
                                        </div>
                                        <div style="font-size: 12px; color: #666;">
                                            点击上方链接打开订阅门户页面
                                        </div>
                                    `;

                                    // 关闭之前打开的订阅页面
                                    if (subscriptionWindow && !subscriptionWindow.closed) {
                                        subscriptionWindow.close();
                                    }
                                }
                            }
                        } catch (error) {
                            console.error('处理订阅响应失败:', error);
                        }
                    },
                    onerror: function(error) {
                        console.error('请求订阅API失败:', error);
                    }
                });
            } catch (error) {
                statusDiv.innerHTML = `打开订阅页面失败: ${error.message}`;
                console.error('打开订阅页面失败:', error);
            }
        });

        // 清空邮箱按钮
        ui.querySelector('#clear-mailbox-btn').addEventListener('click', async () => {
            if (!currentEmail) {
                statusDiv.innerHTML = '请先生成邮箱';
                return;
            }

            try {
                statusDiv.innerHTML = '正在清空邮箱...';

                // 创建一个新的邮件客户端实例
                const tempMailClient = new MailCX();

                // 尝试删除邮箱
                await tempMailClient.authorize();
                await tempMailClient.deleteMailbox(currentEmail);

                statusDiv.innerHTML = `邮箱 <span class="register-helper-email">${currentEmail}</span> 已清空`;
            } catch (error) {
                statusDiv.innerHTML = `清空邮箱失败: ${error.message}`;
                console.error('清空邮箱失败:', error);
            }
        });
    }

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();

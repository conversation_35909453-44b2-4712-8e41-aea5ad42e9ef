<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱配置功能测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>📧 邮箱配置功能测试页面</h1>
    
    <div class="info">
        <strong>测试说明：</strong><br>
        1. 确保已安装并启用修改后的油猴脚本<br>
        2. 在油猴菜单中应该能看到"设置配置"选项<br>
        3. 点击"设置配置"打开设置界面<br>
        4. 配置自定义邮箱后缀和@uuf.me邮箱<br>
        5. 在下方表单中测试邮箱自动填充功能
    </div>

    <div class="test-section">
        <h2 class="test-title">🔧 功能测试区域</h2>
        
        <div class="form-group">
            <label for="email-field">邮箱地址（用于测试自动填充）</label>
            <input type="email" id="email-field" name="email" placeholder="请使用脚本生成并填充邮箱">
        </div>
        
        <div class="form-group">
            <label for="code-field">验证码（用于测试自动填充）</label>
            <input type="text" id="code-field" name="code" placeholder="请输入验证码" maxlength="6">
        </div>
        
        <button class="btn" onclick="testEmailDetection()">测试邮箱输入框检测</button>
        <button class="btn" onclick="testCodeDetection()">测试验证码输入框检测</button>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 测试步骤</h2>
        <ol>
            <li><strong>打开设置：</strong>点击油猴菜单中的"设置配置"</li>
            <li><strong>配置邮箱后缀：</strong>输入自定义域名（如：mydomain.com）</li>
            <li><strong>配置转发邮箱：</strong>输入@uuf.me邮箱地址</li>
            <li><strong>保存设置：</strong>点击保存按钮</li>
            <li><strong>测试生成：</strong>在脚本主界面生成邮箱，检查是否使用自定义后缀</li>
            <li><strong>测试填充：</strong>点击"填充到页面"按钮，检查是否正确填入上方输入框</li>
        </ol>
    </div>

    <div class="test-section">
        <h2 class="test-title">✅ 预期结果</h2>
        <ul>
            <li>油猴菜单中显示"设置配置"选项</li>
            <li>设置界面能正常打开和关闭</li>
            <li>输入验证功能正常工作</li>
            <li>设置能正确保存和加载</li>
            <li>邮箱生成使用自定义后缀</li>
            <li>邮箱和验证码能正确填充到页面输入框</li>
        </ul>
    </div>

    <script>
        function testEmailDetection() {
            const emailField = document.getElementById('email-field');
            emailField.style.border = '2px solid #28a745';
            emailField.focus();
            alert('邮箱输入框已高亮显示，请测试脚本的邮箱填充功能');
            setTimeout(() => {
                emailField.style.border = '1px solid #ddd';
            }, 3000);
        }

        function testCodeDetection() {
            const codeField = document.getElementById('code-field');
            codeField.style.border = '2px solid #ffc107';
            codeField.focus();
            alert('验证码输入框已高亮显示，请测试脚本的验证码填充功能');
            setTimeout(() => {
                codeField.style.border = '1px solid #ddd';
            }, 3000);
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('测试页面已加载，请确保油猴脚本已启用');
        });
    </script>
</body>
</html>

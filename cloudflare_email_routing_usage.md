# AugmentCode Token Helper with Cloudflare Email Routing 使用说明

## 概述
这是一个增强版的AugmentCode Token Helper用户脚本，集成了Cloudflare Email Routing功能，支持自动验证码获取。

## 核心特性
- **双邮箱系统**：前端邮箱用于注册，接收邮箱用于API获取验证码
- **Cloudflare Email Routing集成**：利用catch-all转发功能
- **MailCX API集成**：自动获取和解析验证码邮件
- **半自动化流程**：手动触发验证码获取，保持用户控制

## 工作原理

### 邮箱流转过程
1. **前端邮箱**：`随机前缀@自定义域名` (用于注册)
2. **Cloudflare转发**：自动转发到接收邮箱
3. **接收邮箱**：`用户配置@uuf.me` (用于API获取)
4. **验证码提取**：通过MailCX API自动获取和解析

### 技术架构
- **前端框架**：Vue.js + jQuery (保持原有技术栈)
- **API客户端**：MailCX API (移植自Python版本)
- **跨域请求**：GM_xmlhttpRequest
- **数据存储**：localStorage + GM_setValue

## 使用步骤

### 1. Cloudflare Email Routing配置
在Cloudflare控制台中：
1. 进入你的域名管理页面
2. 选择"Email" -> "Email Routing"
3. 设置catch-all规则：`*@yourdomain.com` -> `<EMAIL>`
4. 确保DNS记录正确配置

### 2. 脚本配置
1. 安装用户脚本到Tampermonkey
2. 访问AugmentCode网站
3. 点击脚本界面的"设置"标签页
4. 配置以下信息：
   - **自定义域名后缀**：你的域名（如：example.com）
   - **接收邮箱**：你的@uuf.me邮箱（如：<EMAIL>）
5. 点击"保存设置"
6. 点击"测试连接"验证API是否正常

### 3. 注册流程
1. 在"注册助手"标签页点击"生成邮箱"
2. 系统生成前端邮箱：`<EMAIL>`
3. 点击"填充到页面"将邮箱填入注册表单
4. 在注册页面提交邮箱信息
5. 等待几秒后点击"获取验证码"
6. 系统自动从接收邮箱获取验证码
7. 点击"填充验证码"完成注册

## 配置参数说明

### 邮箱生成配置
- **前缀长度**：10-15位随机字符
- **字符集**：小写字母 + 数字
- **格式**：`{随机前缀}@{自定义域名}`

### 验证码提取配置
支持多种验证码格式：
- `code is 123456`
- `verification code is: 123456`
- `your code: 123456`
- 任意6位数字

### API轮询配置
- **轮询间隔**：5秒
- **最大尝试次数**：12次（总计1分钟）
- **超时处理**：自动停止并报错

## 故障排除

### 常见问题
1. **验证码获取失败**
   - 检查Cloudflare Email Routing配置
   - 确认接收邮箱API连接正常
   - 查看控制台日志获取详细错误信息

2. **邮箱填充失败**
   - 检查页面是否有邮箱输入框
   - 尝试手动复制邮箱地址
   - 确认页面加载完成

3. **API连接失败**
   - 检查网络连接
   - 确认@uuf.me邮箱地址正确
   - 尝试重新授权API

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console标签页的日志输出
3. 所有操作都有详细的日志记录
4. 日志格式：`[时间] [TokenHelper-CF] 消息内容`

## 安全注意事项
- 接收邮箱仅用于验证码获取，不存储敏感信息
- 验证码获取后立即删除邮件
- 所有配置信息存储在本地浏览器中
- 不会上传任何用户数据到第三方服务器

## 技术支持
如遇到问题，请提供：
1. 浏览器控制台的完整日志
2. Cloudflare Email Routing配置截图
3. 脚本配置信息（隐藏敏感信息）
4. 具体的错误现象描述

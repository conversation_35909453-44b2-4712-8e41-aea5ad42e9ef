// ==UserScript==
// @name         Nc.me表单自动填充生成器(API版)
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  使用randomuser.me API生成随机数据并一键填充表单，支持数据保存和恢复，导出功能
// <AUTHOR> 0.46.11
// @match        *://*.namecheap.com/*
// @match        *://*.nc.me/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// ==/UserScript==

(function() {
    'use strict';

    // 数据生成工具 - 使用randomuser.me API
    const DataGenerator = {
        // 生成随机密码
        getPassword: function(length, includeSpecial) {
            length = length || 12;
            let chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            if (includeSpecial) {
                // 只使用简单的特殊字符，避免使用可能导致显示问题的字符
                chars += "!@#$%^&*()_+-=";
            }
            return this.randomString(length, chars);
        },
        
        // 生成随机字符串
        randomString: function(length, chars) {
            chars = chars || 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        },
        
        // 生成随机整数
        randomInt: function(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        },
        
        // 使用randomuser.me API生成完整个人信息数据集
        generatePersonData: function() {
            return new Promise((resolve, reject) => {
                // 显示加载提示
                if (window.UI && window.UI.showStatusMessage) {
                    window.UI.showStatusMessage('正在从API获取随机用户数据...', 'info');
                }
                
                // 使用GM_xmlhttpRequest避免CORS问题
                GM_xmlhttpRequest({
                    method: "GET",
                    url: "https://randomuser.me/api/?nat=us",
                    timeout: 10000, // 10秒超时
                    onload: (response) => {
                        try {
                            if (response.status !== 200) {
                                if (window.UI && window.UI.showStatusMessage) {
                                    window.UI.showStatusMessage(`API错误: ${response.status}`, 'error');
                                }
                                reject(new Error(`API响应错误: ${response.status}`));
                                return;
                            }
                            
                            const data = JSON.parse(response.responseText).results[0];
                            
                            // 使用本地逻辑生成密码
                            const password = this.getPassword(12, true);
                            
                            // 邮箱处理 - 默认使用API返回的用户名
                            let username = data.login.username;
                            let email = `${username}@uuf.me`;
                            
                            // 如果UI已初始化并且有域名值，我们将在调用方法后覆盖这些值
                            // 这里只是生成初始数据
                            
                            // 构建结果对象
                            const result = {
                                username: username,
                                password: password,
                                email: email,
                                firstName: data.name.first,
                                lastName: data.name.last,
                                addressLine1: `${data.location.street.number} ${data.location.street.name}`,
                                addressLine2: `Apt ${this.randomInt(1, 999)}`,
                                city: data.location.city,
                                state: data.location.state,
                                stateAbbr: "", // API不提供缩写
                                postalCode: data.location.postcode.toString(),
                                phone: data.phone,
                                timestamp: new Date().toLocaleString(),
                                filled: false // 添加填充状态标记
                            };
                            
                            if (window.UI && window.UI.showStatusMessage) {
                                window.UI.showStatusMessage('用户数据获取成功', 'success');
                            }
                            
                            resolve(result);
                        } catch (error) {
                            console.error("解析API数据失败:", error);
                            if (window.UI && window.UI.showStatusMessage) {
                                window.UI.showStatusMessage('解析API数据失败', 'error');
                            }
                            reject(error);
                        }
                    },
                    onerror: (error) => {
                        console.error("API请求失败:", error);
                        if (window.UI && window.UI.showStatusMessage) {
                            window.UI.showStatusMessage('API请求失败', 'error');
                        }
                        reject(error);
                    },
                    ontimeout: () => {
                        console.error("API请求超时");
                        if (window.UI && window.UI.showStatusMessage) {
                            window.UI.showStatusMessage('API请求超时', 'error');
                        }
                        reject(new Error('API请求超时'));
                    }
                });
            });
        },
        
        // 从数组中随机选择一个元素
        randomElement: function(array) {
            return array[this.randomInt(0, array.length - 1)];
        }
    };

    // 数据存储工具 - 使用GM API
    const DataStorage = {
        storageKey: 'formFiller_NamecheapData',
        tempDataKey: 'formFiller_TempData', // 用于存储临时生成的数据
        
        // 检查用户名是否已存在 
        isUsernameDuplicate: function(username) {
            const allData = this.getAllData();
            return allData.some(item => item.username === username);
        },
        
        // 保存数据
        saveData: function(data) {
            try {
                // 首先检查用户名是否已经存在
                if (this.isUsernameDuplicate(data.username)) {
                    return { success: false, message: '该用户名已存在于保存的数据中' };
                }
                
                // 获取现有数据
                let savedData = this.getAllData();
                
                // 生成唯一ID
                data.id = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
                savedData.push(data);
                
                // 使用GM_setValue保存数据
                GM_setValue(this.storageKey, savedData);
                
                console.log("数据已保存", data);
                return { success: true, id: data.id };
            } catch(e) {
                console.error("保存数据失败:", e);
                return { success: false, message: "保存数据失败: " + e.message };
            }
        },
        
        // 保存临时生成的数据（用于跨页面保持）
        saveTempData: function(data) {
            try {
                GM_setValue(this.tempDataKey, data);
                console.log("临时数据已保存", data);
            } catch(e) {
                console.error("保存临时数据失败:", e);
            }
        },
        
        // 获取临时生成的数据
        getTempData: function() {
            try {
                return GM_getValue(this.tempDataKey, null);
            } catch(e) {
                console.error("读取临时数据失败:", e);
                return null;
            }
        },
        
        // 清除临时数据
        clearTempData: function() {
            GM_setValue(this.tempDataKey, null);
        },
        
        // 获取所有保存的数据
        getAllData: function() {
            try {
                // 使用GM_getValue获取数据
                const data = GM_getValue(this.storageKey, []);
                console.log("读取的数据:", data);
                return data;
            } catch(e) {
                console.error("读取数据失败:", e);
                return [];
            }
        },
        
        // 获取指定ID的数据
        getData: function(id) {
            const allData = this.getAllData();
            return allData.find(item => item.id === id);
        },
        
        // 删除数据
        deleteData: function(id) {
            let allData = this.getAllData();
            allData = allData.filter(item => item.id !== id);
            GM_setValue(this.storageKey, allData);
        },
        
        // 清空所有数据
        clearAllData: function() {
            GM_setValue(this.storageKey, []);
        },
        
        // 导出所有数据为文本
        exportDataAsText: function() {
            const allData = this.getAllData();
            if (allData.length === 0) {
                return "没有保存的数据";
            }
            
            let text = "表单填充数据导出 - " + new Date().toLocaleString() + "\n\n";
            
            allData.forEach((data, index) => {
                text += `--- 记录 ${index + 1} ---\n`;
                text += `姓名: ${data.firstName} ${data.lastName}\n`;
                text += `用户名: ${data.username}\n`;
                text += `密码: ${data.password}\n`;
                text += `邮箱: ${data.email || '未填写'}\n`;
                text += `地址: ${data.addressLine1}\n`;
                text += `地址2: ${data.addressLine2 || '无'}\n`;
                text += `城市: ${data.city}\n`;
                text += `州/省: ${data.state}\n`;
                text += `邮编: ${data.postalCode}\n`;
                text += `电话: ${data.phone}\n`;
                text += `保存时间: ${data.timestamp || '未知'}\n\n`;
            });
            
            return text;
        }
    };

    // 显示UI和交互逻辑
    const UI = {
        panel: null,
        dataContainer: null,
        savedDataContainer: null,
        saveButton: null,
        showSavedData: false,
        buttonContainer: null,
        secondRowContainer: null,
        notificationArea: null,
        generatedData: null,
        hasFilledForm: false,
        userFilledEmail: null,
        emailFilled: false,
        domainInput: null, // 域名输入框
        domainValue: "", // 存储域名值
        domainInputTimer: null, // 添加域名输入防抖计时器
        
        // 初始化UI
        init: function() {
            // 添加域名输入防抖计时器
            this.domainInputTimer = null;
            
            // 检测当前页面类型
            const isSignInPage = window.location.href.includes('/login') || 
                                window.location.href.includes('/signin');
            
            // 创建控制面板 - 统一上边界高度
            this.panel = document.createElement('div');
            this.panel.style.cssText = `
                position: fixed;
                top: 200px;
                left: 20px;
                width: 350px;
                background: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                z-index: 10000;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                max-height: ${isSignInPage ? '90vh' : '75vh'};
                overflow-y: auto;
            `;

            // 创建标题 - 缩小边距
            const title = document.createElement('h3');
            title.textContent = 'Nc.me表单填写助手(API版)';
            title.style.cssText = `
                margin: 0 0 8px 0; 
                color: #333; 
                font-size: 16px; 
                text-align: center; 
                padding: 6px 0; 
                background-color: #f1f8e9; 
                border-radius: 4px; 
                border-bottom: 2px solid #4CAF50;
                font-weight: bold;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            `;
            this.panel.appendChild(title);
            
            // 创建通知区域 - 在标题下方
            this.notificationArea = document.createElement('div');
            this.notificationArea.style.cssText = `
                margin: -10px 0 10px 0;
                padding: 0;
                min-height: 30px;
                text-align: center;
            `;
            this.panel.appendChild(this.notificationArea);
            
            // 如果是登录页面，只显示保存的数据
            if (isSignInPage) {
                this.savedDataContainer = document.createElement('div');
                this.savedDataContainer.id = 'saved-data';
                this.panel.appendChild(this.savedDataContainer);
                
                // 显示保存的数据
                this.displaySavedData();
            } else {
                // 注册页面的完整UI
                // 在数据容器上方添加域名输入框
                const domainInputContainer = document.createElement('div');
                domainInputContainer.style.cssText = 'margin-bottom: 15px; padding: 5px;';
                
                const domainLabel = document.createElement('label');
                domainLabel.textContent = '域名:';
                domainLabel.style.cssText = 'font-weight: bold; margin-right: 10px;';
                domainInputContainer.appendChild(domainLabel);
                
                this.domainInput = document.createElement('input');
                this.domainInput.type = 'text';
                this.domainInput.placeholder = '输入您的域名';
                this.domainInput.style.cssText = `
                    width: 70%;
                    padding: 5px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                `;
                
                this.domainInput.addEventListener('input', (e) => {
                    this.domainValue = e.target.value.trim();
                    
                    // 如果域名非空，触发数据生成
                    if (this.domainValue) {
                        // 添加防抖，避免频繁API调用
                        clearTimeout(this.domainInputTimer);
                        this.domainInputTimer = setTimeout(() => {
                            // 显示正在生成数据的提示
                            this.showStatusMessage('正在根据域名生成新数据...', 'info');
                            
                            // 调用API生成全新数据
                            DataGenerator.generatePersonData()
                                .then(data => {
                                    this.generatedData = data;
                                    
                                    // 使用域名前缀作为用户名和邮箱
                                    const domainPrefix = this.extractDomainPrefix(this.domainValue);
                                    if (domainPrefix) {
                                        this.generatedData.username = domainPrefix;
                                        // 确保始终使用域名前缀作为邮箱
                                        this.generatedData.email = `${domainPrefix}@uuf.me`;
                                    }
                                    
                                    // 保存域名
                                    this.generatedData.domain = this.domainValue;
                                    
                                    // 保存临时数据，用于页面刷新后恢复
                                    DataStorage.saveTempData(this.generatedData);
                                    
                                    // 更新显示
                                    this.displayCurrentData();
                                    this.showStatusMessage('已根据域名生成新数据', 'success');
                                })
                                .catch(error => {
                                    console.error('生成数据失败:', error);
                                    this.showStatusMessage('生成数据失败: ' + error.message, 'error');
                                });
                        }, 800); // 800毫秒延迟，避免用户还在输入时就触发
                    }
                });
                
                domainInputContainer.appendChild(this.domainInput);
                
                // 添加提示信息
                const domainHint = document.createElement('p');
                domainHint.textContent = '用户名将使用域名前缀';
                domainHint.style.cssText = 'color: #666; font-size: 12px; margin: 3px 0 0 0; font-style: italic;';
                domainInputContainer.appendChild(domainHint);
                
                this.panel.appendChild(domainInputContainer);
                
                // 创建数据容器
                this.dataContainer = document.createElement('div');
                this.dataContainer.id = 'data-container';
                this.panel.appendChild(this.dataContainer);

                // 创建保存数据容器
                this.savedDataContainer = document.createElement('div');
                this.savedDataContainer.id = 'saved-data-container';
                this.savedDataContainer.style.display = 'none';
                this.panel.appendChild(this.savedDataContainer);

                // 创建按钮容器
                this.buttonContainer = document.createElement('div');
                this.buttonContainer.id = 'button-container';
                this.buttonContainer.style.cssText = 'display: flex; margin-top: 10px; gap: 6px; flex-wrap: wrap;';
                this.panel.appendChild(this.buttonContainer);

                // 创建生成按钮
                const generateButton = document.createElement('button');
                generateButton.textContent = '重新生成';
                generateButton.style.cssText = `
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    flex: 1;
                    min-width: 100px;
                    font-size: 13px;
                `;
                generateButton.onclick = () => {
                    // 检查域名是否已输入
                    if (!this.domainValue) {
                        this.showStatusMessage('请先输入域名', 'warning');
                        return;
                    }
                    
                    this.showStatusMessage('正在请求API数据...', 'info');
                    
                    DataGenerator.generatePersonData()
                        .then(data => {
                            this.generatedData = data;
                            
                            // 使用域名前缀作为用户名和邮箱
                            const domainPrefix = this.extractDomainPrefix(this.domainValue);
                            if (domainPrefix) {
                                this.generatedData.username = domainPrefix;
                                // 确保始终使用域名前缀作为邮箱
                                this.generatedData.email = `${domainPrefix}@uuf.me`;
                            }
                            
                            this.hasFilledForm = false;
                            this.emailFilled = false;
                            this.userFilledEmail = "";
                            
                            // 保存临时数据，用于跨页面使用
                            DataStorage.saveTempData(this.generatedData);
                            
                            // 重置邮箱表单字段
                            const emailField = findFormField('email');
                            if (emailField) {
                                emailField.value = '';
                                emailField.dispatchEvent(new Event('input', { bubbles: true }));
                                emailField.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                            
                            this.displayCurrentData();
                        })
                        .catch(error => {
                            console.error('生成数据失败:', error);
                            this.showStatusMessage('生成数据失败: ' + error.message, 'error');
                        });
                };
                this.buttonContainer.appendChild(generateButton);

                // 创建填充按钮
                const fillButton = document.createElement('button');
                fillButton.textContent = '一键填充';
                fillButton.style.cssText = `
                    background: #2196F3;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    flex: 1;
                    min-width: 100px;
                    font-size: 13px;
                `;
                fillButton.onclick = () => {
                    if(!this.generatedData) {
                        this.showStatusMessage('请先生成数据', 'warning');
                        return;
                    }
                    fillForm();
                    this.hasFilledForm = true;
                };
                this.buttonContainer.appendChild(fillButton);

                // 创建保存按钮
                this.saveButton = document.createElement('button');
                this.saveButton.textContent = '保存数据';
                this.saveButton.style.cssText = `
                    background: #FF9800;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    flex: 1;
                    min-width: 100px;
                    font-size: 13px;
                `;
                this.saveButton.onclick = () => {
                    // 直接使用生成的数据，不从表单读取
                    if (!this.generatedData) {
                        this.showStatusMessage('请先生成数据', 'warning');
                        return;
                    }
                    
                    // 确保域名信息也被保存
                    if (this.domainValue) {
                        this.generatedData.domain = this.domainValue;
                    }
                    
                    const result = DataStorage.saveData(this.generatedData);
                    if (result.success) {
                        this.showStatusMessage('数据已保存', 'success');
                    } else {
                        this.showStatusMessage(result.message, 'warning');
                    }
                };
                this.buttonContainer.appendChild(this.saveButton);

                // 第二行按钮
                this.secondRowContainer = document.createElement('div');
                this.secondRowContainer.style.cssText = 'display: flex; margin-top: 10px; gap: 10px;';
                this.panel.appendChild(this.secondRowContainer);

                // 创建数据切换按钮
                const toggleButton = document.createElement('button');
                toggleButton.textContent = '查看保存的数据';
                toggleButton.id = 'toggle-data-view-btn';
                toggleButton.style.cssText = `
                    background: #757575;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 4px;
                    cursor: pointer;
                    flex: 1;
                `;
                toggleButton.onclick = () => this.toggleDataView();
                this.secondRowContainer.appendChild(toggleButton);

                // 创建导出按钮
                const exportButton = document.createElement('button');
                exportButton.textContent = '导出数据';
                exportButton.style.cssText = `
                    background: #9C27B0;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 4px;
                    cursor: pointer;
                    flex: 1;
                `;
                exportButton.onclick = () => this.exportData();
                this.secondRowContainer.appendChild(exportButton);
                
                // 显示初始界面
                this.displayCurrentData();
                
                // 检查是否有临时保存的数据
                const tempData = DataStorage.getTempData();
                if (tempData) {
                    this.generatedData = tempData;
                    
                    // 如果有保存的域名，恢复它
                    if (tempData.domain) {
                        this.domainValue = tempData.domain;
                        this.domainInput.value = this.domainValue;
                    }
                    
                    this.displayCurrentData();
                    this.showStatusMessage('已恢复上次生成的数据', 'info');
                }
                
                // 添加对表单中邮箱字段的监听
                this.setupEmailListener();
            }

            // 添加面板到文档
            document.body.appendChild(this.panel);
        },
        
        // 邮箱字段的监听器
        setupEmailListener: function() {
            // 使用MutationObserver监听DOM变化
            const observer = new MutationObserver(() => {
                // 尝试查找邮箱字段
                const emailField = findFormField('email');
                if (emailField) {
                    // 获取当前值（如果有的话）
                    if (emailField.value && emailField.value.trim() !== '') {
                        this.userFilledEmail = emailField.value;
                        this.emailFilled = true;
                    }
                    
                    // 为邮箱字段添加事件监听器
                    emailField.addEventListener('input', (e) => {
                        this.userFilledEmail = e.target.value;
                        this.emailFilled = this.userFilledEmail.trim() !== '';
                    });
                    
                    // 监听到邮箱字段后可以停止观察
                    observer.disconnect();
                }
            });
            
            // 开始观察文档变化
            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });
            
            // 立即尝试查找一次
            const emailField = findFormField('email');
            if (emailField) {
                // 获取当前值（如果有的话）
                if (emailField.value && emailField.value.trim() !== '') {
                    this.userFilledEmail = emailField.value;
                    this.emailFilled = true;
                }
                
                emailField.addEventListener('input', (e) => {
                    this.userFilledEmail = e.target.value;
                    this.emailFilled = this.userFilledEmail.trim() !== '';
                });
            }
        },
        
        // 导出数据为文本文件
        exportData: function() {
            const text = DataStorage.exportDataAsText();
            const blob = new Blob([text], {type: 'text/plain;charset=utf-8'});
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `Nc.me账户数据_${new Date().toISOString().slice(0,10)}.txt`;
            document.body.appendChild(a);
            a.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
            
            this.showStatusMessage('数据已导出', 'success');
        },

        displayCurrentData: function() {
            // 创建表格显示数据
            if (this.generatedData) {
                let html = '<table style="width:100%; border-collapse: collapse; font-size: 14px;">';
                
                for (const [key, value] of Object.entries(this.generatedData)) {
                    // 不显示一些重复或不必要的字段
                    if (key === 'id' || key === 'timestamp' || key === 'stateAbbr' || key === 'filled') continue;
                    
                    // 正常显示所有字段值，包括邮箱
                    html += `
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 4px 0; color: #666; font-weight: bold;">${formatKey(key)}:</td>
                            <td style="padding: 4px 0;">${value}</td>
                        </tr>
                    `;
                }
                
                html += '</table>';
                this.dataContainer.innerHTML = html;
            } else {
                this.dataContainer.innerHTML = '<p style="text-align:center; color:#666;">没有当前数据</p>';
            }
        },

        displaySavedData: function() {
            const savedData = DataStorage.getAllData();
            const isSignInPage = window.location.href.includes('/login') || 
                                window.location.href.includes('/signin');
            
            if (savedData.length === 0) {
                this.savedDataContainer.innerHTML = '<p style="text-align:center; color:#666;">没有保存的数据</p>';
                return;
            }
            
            let html = '';
            
            // 调整登录页面按钮布局
            if (!isSignInPage) {
                // 注册页面的清空按钮 
                html += `
                    <div style="margin-bottom: 10px;">
                        <button id="clear-all-data" style="background:#f44336; color:white; border:none; padding:5px 10px; border-radius:4px; cursor:pointer;">
                            清空所有数据
                        </button>
                    </div>
                `;
            } else {
                // 登录页面 - 左右两侧分别放置清空和导出按钮
                html += `
                    <div style="display:flex; justify-content:space-between; margin-bottom: 8px;">
                        <button id="clear-all-data" style="background:#f44336; color:white; border:none; padding:3px 6px; border-radius:3px; cursor:pointer; font-size: 11px;">
                            清空所有数据
                        </button>
                        <button id="export-data" style="background:#9C27B0; color:white; border:none; padding:3px 6px; border-radius:3px; cursor:pointer; font-size: 11px;">
                            导出数据
                        </button>
                    </div>
                `;
            }
            
            html += '<div style="max-height: 400px; overflow-y: auto;">';
            
            savedData.forEach(data => {
                // 显示邮箱，如果没有则显示提示文本
                const emailDisplay = data.email && data.email !== "用于验证的邮箱" && data.email !== "请自行填写用于验证的邮箱" 
                    ? data.email 
                    : '<span style="color:#999;">未填写邮箱</span>';
                
                if (isSignInPage) {
                    // 登录页面 - 更紧凑的卡片
                    html += `
                        <div style="margin-bottom: 6px; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                <span style="font-weight: bold; overflow: hidden; text-overflow: ellipsis; max-width: 200px; font-size: 13px;">${emailDisplay}</span>
                                <button class="copy-btn" data-text="${data.email}" style="background:#4CAF50; color:white; border:none; padding:1px 6px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                            
                            <div style="margin-bottom: 3px; display: flex; justify-content: space-between; align-items: center; font-size: 13px;">
                                <span>用户名: ${data.username}</span>
                                <button class="copy-btn" data-text="${data.username}" style="background:#4CAF50; color:white; border:none; padding:1px 6px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 13px;">
                                <span>密码: ${data.password}</span>
                                <button class="copy-btn" data-text="${data.password}" style="background:#4CAF50; color:white; border:none; padding:1px 6px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    // 注册页面 - 更紧凑的卡片
                    html += `
                        <div style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                <span style="font-weight: bold; overflow: hidden; text-overflow: ellipsis; max-width: 200px;">${emailDisplay}</span>
                                <button class="copy-btn" data-text="${data.email}" style="background:#4CAF50; color:white; border:none; padding:1px 5px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                            
                            <div style="margin-bottom: 3px; display: flex; justify-content: space-between; align-items: center;">
                                <span>用户名: ${data.username}</span>
                                <button class="copy-btn" data-text="${data.username}" style="background:#4CAF50; color:white; border:none; padding:1px 5px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                            
                            <div style="margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                <span>密码: ${data.password}</span>
                                <button class="copy-btn" data-text="${data.password}" style="background:#4CAF50; color:white; border:none; padding:1px 5px; border-radius:3px; cursor:pointer; font-size: 11px;">
                                    复制
                                </button>
                            </div>
                            
                            <div style="display: flex; gap: 4px;">
                                <button data-id="${data.id}" class="load-data-btn" style="background:#2196F3; color:white; border:none; padding:2px 6px; border-radius:3px; cursor:pointer; flex:1; font-size: 11px;">
                                    加载
                                </button>
                                <button data-id="${data.id}" class="delete-data-btn" style="background:#f44336; color:white; border:none; padding:2px 6px; border-radius:3px; cursor:pointer; flex:1; font-size: 11px;">
                                    删除
                                </button>
                            </div>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
            this.savedDataContainer.innerHTML = html;
            
            // 添加事件监听
            setTimeout(() => {
                // 复制按钮事件监听
                this.savedDataContainer.querySelectorAll('.copy-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const textToCopy = e.target.getAttribute('data-text');
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            this.showStatusMessage('已复制到剪贴板', 'success');
                        }).catch(err => {
                            console.error('复制失败:', err);
                            this.showStatusMessage('复制失败', 'error');
                        });
                    });
                });
                
                // 非登录页面的按钮事件
                if (!isSignInPage) {
                    this.savedDataContainer.querySelectorAll('.load-data-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            const id = e.target.getAttribute('data-id');
                            this.loadSavedData(id);
                        });
                    });
                    
                    this.savedDataContainer.querySelectorAll('.delete-data-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            const id = e.target.getAttribute('data-id');
                            this.deleteSavedData(id);
                        });
                    });
                }
                
                // 清空按钮 - 两种页面都有
                const clearAllBtn = document.getElementById('clear-all-data');
                if (clearAllBtn) {
                    clearAllBtn.addEventListener('click', () => this.clearAllSavedData());
                }
                
                // 登录页面专用导出按钮
                if (isSignInPage) {
                    const exportBtn = document.getElementById('export-data');
                    if (exportBtn) {
                        exportBtn.addEventListener('click', () => this.exportData());
                    }
                }
            }, 0);
        },
        
        // 切换数据视图（当前/保存的）
        toggleDataView: function() {
            this.showSavedData = !this.showSavedData;
            
            if (this.showSavedData) {
                this.dataContainer.style.display = 'none';
                this.savedDataContainer.style.display = 'block';
                this.displaySavedData();
                
                // 隐藏主操作按钮
                this.buttonContainer.style.display = 'none';
                
                // 更新切换按钮文字
                const toggleBtn = document.getElementById('toggle-data-view-btn');
                if (toggleBtn) toggleBtn.textContent = '返回当前数据';
            } else {
                this.dataContainer.style.display = 'block';
                this.savedDataContainer.style.display = 'none';
                this.displayCurrentData();
                
                // 显示主操作按钮
                this.buttonContainer.style.display = 'flex';
                
                // 更新切换按钮文字
                const toggleBtn = document.getElementById('toggle-data-view-btn');
                if (toggleBtn) toggleBtn.textContent = '查看保存的数据';
            }
        },
        
        // 加载保存的数据
        loadSavedData: function(id) {
            const data = DataStorage.getData(id);
            if (data) {
                this.generatedData = data;
                
                // 如果数据中有域名，恢复到输入框
                if (data.domain && this.domainInput) {
                    this.domainValue = data.domain;
                    this.domainInput.value = this.domainValue;
                }
                
                // 同时保存为临时数据
                DataStorage.saveTempData(data);
                this.hasFilledForm = false;
                this.displayCurrentData();
                this.toggleDataView();
                this.showStatusMessage('数据已加载', 'info');
            }
        },
        
        // 删除保存的数据
        deleteSavedData: function(id) {
            if (confirm('确定要删除这条数据吗？')) {
                DataStorage.deleteData(id);
                this.displaySavedData();
                this.showStatusMessage('数据已删除', 'success');
            }
        },
        
        // 清空所有保存的数据
        clearAllSavedData: function() {
            if (confirm('确定要删除所有保存的数据吗？')) {
                DataStorage.clearAllData();
                this.displaySavedData();
                this.showStatusMessage('所有数据已清空', 'success');
            }
        },
        
        // 显示状态消息 - 改为在面板内显示
        showStatusMessage: function(message, type = 'info') {
            // 清除旧消息
            const oldMessages = this.notificationArea.querySelectorAll('.status-message');
            oldMessages.forEach(msg => msg.remove());
            
            // 创建新消息
            const statusDiv = document.createElement('div');
            statusDiv.textContent = message;
            statusDiv.className = 'status-message';
            
            // 根据消息类型设置不同样式
            let bgColor, textColor = 'white';
            switch(type) {
                case 'success':
                    bgColor = '#4CAF50';
                    break;
                case 'warning':
                    bgColor = '#FF9800';
                    break;
                case 'error':
                    bgColor = '#F44336';
                    break;
                default:
                    bgColor = '#2196F3'; // info
            }
            
            statusDiv.style.cssText = `
                padding: 8px 12px;
                border-radius: 4px;
                color: ${textColor};
                margin: 5px 0;
                animation: fadeIn 0.3s ease-in-out;
                background-color: ${bgColor};
            `;
            
            // 添加到通知区域
            this.notificationArea.appendChild(statusDiv);
            
            // 自动隐藏
            setTimeout(() => {
                statusDiv.style.animation = 'fadeOut 0.5s ease-in-out forwards';
                setTimeout(() => statusDiv.remove(), 500);
            }, 3000);
        },
        
        // 提取域名前缀
        extractDomainPrefix: function(domain) {
            // 移除http://或https://
            let cleanDomain = domain.replace(/^https?:\/\//, '');
            
            // 移除www.
            cleanDomain = cleanDomain.replace(/^www\./, '');
            
            // 获取.之前的部分
            const dotIndex = cleanDomain.indexOf('.');
            if (dotIndex > 0) {
                return cleanDomain.substring(0, dotIndex);
            }
            
            // 如果没有.，返回整个域名
            return cleanDomain;
        },
        
        // 保存临时数据
        saveTempData: function() {
            if (this.generatedData) {
                // 将域名也保存到数据中
                this.generatedData.domain = this.domainValue;
                DataStorage.saveTempData(this.generatedData);
            }
        }
    };

    // 格式化键名为更友好的显示
    function formatKey(key) {
        return key
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/^./, str => str.toUpperCase()) // 首字母大写
            .replace(/([a-z])(\d)/, '$1 $2') // 在字母和数字之间添加空格
            .replace('Line', ' Line '); // 处理地址行
    }

    // 填充表单
    function fillForm() {
        // 查找表单字段
        const usernameField = findFormField('username', 'user', 'login');
        const passwordField = findFormField('password', 'pass');
        const confirmPasswordField = findFormField('password_confirmation', 'confirm', 'repeat', 'verif');
        const emailField = findFormField('email');
        const firstNameField = findFormField('first_name', 'firstname', 'fname', 'first name', 'name');
        const lastNameField = findFormField('last_name', 'lastname', 'lname', 'last name', 'surname');
        
        const addressLine1Field = findFormField('address_1', 'address1', 'address-line-1', 'address_line_1', 'address', 'street', 'line1');
        const addressLine2Field = findFormField('address_2', 'address2', 'address-line-2', 'address_line_2', 'apt', 'suite', 'line2');
        
        const cityField = findFormField('city', 'town');
        const stateField = findFormField('state', 'province', 'region');
        const postalCodeField = findFormField('zipcode', 'postal', 'zip', 'postcode');
        const phoneField = findFormField('phone_intl', 'phone', 'telephone', 'mobile', 'cell');

        // 填充字段
        fillField(usernameField, UI.generatedData.username);
        fillField(passwordField, UI.generatedData.password);
        fillField(confirmPasswordField, UI.generatedData.password);
        fillField(emailField, UI.generatedData.email);
        fillField(firstNameField, UI.generatedData.firstName);
        fillField(lastNameField, UI.generatedData.lastName);
        fillField(addressLine1Field, UI.generatedData.addressLine1);
        fillField(addressLine2Field, UI.generatedData.addressLine2);
        fillField(cityField, UI.generatedData.city);
        fillField(stateField, UI.generatedData.state);
        fillField(postalCodeField, UI.generatedData.postalCode);
        fillField(phoneField, UI.generatedData.phone);

        // 处理额外的电话国家代码字段
        const phoneCountryField = document.querySelector('input[name="phone_country"]');
        if (phoneCountryField) {
            phoneCountryField.value = '1'; // 默认美国国家代码
            phoneCountryField.dispatchEvent(new Event('input', { bubbles: true }));
            phoneCountryField.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 处理隐藏的电话字段
        const hiddenPhoneField = document.querySelector('input[name="phone"]');
        if (hiddenPhoneField) {
            hiddenPhoneField.value = UI.generatedData.phone;
            hiddenPhoneField.dispatchEvent(new Event('input', { bubbles: true }));
            hiddenPhoneField.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // 显示填充结果
        UI.showStatusMessage('表单已填充', 'success');
        
        // 标记数据已填充状态
        UI.generatedData.filled = true;
        UI.emailFilled = true; // 自动设置邮箱已填充状态
        
        // 更新显示的数据为表单中的当前值
        UI.generatedData = readFormData();
        UI.displayCurrentData();
    }

    // 通过多种可能的名称查找表单字段
    function findFormField(...possibleNames) {
        for (const name of possibleNames) {
            // 尝试通过id查找
            let element = document.getElementById(name);
            if (element && isInputElement(element)) return element;

            // 尝试通过name查找
            element = document.querySelector(`[name="${name}"], [name="${name.toLowerCase()}"]`);
            if (element && isInputElement(element)) return element;

            // 尝试通过placeholder查找
            element = document.querySelector(`[placeholder*="${name}"], [placeholder*="${name.toLowerCase()}"]`);
            if (element && isInputElement(element)) return element;

            // 查找特定的确认密码和地址字段
            if (name === 'confirm' || name === 'password_confirmation') {
                element = document.querySelector('input[name="password_confirmation"]');
                if (element) return element;
            }
            
            if (name === 'address_1' || name === 'address1') {
                element = document.querySelector('input[name="address_1"]');
                if (element) return element;
            }
            
            if (name === 'address_2' || name === 'address2') {
                element = document.querySelector('input[name="address_2"]');
                if (element) return element;
            }

            // 尝试通过标签相关文本查找
            const labels = Array.from(document.querySelectorAll('label'));
            for (const label of labels) {
                if (label.textContent.toLowerCase().includes(name.toLowerCase())) {
                    // 检查for属性
                    if (label.htmlFor) {
                        element = document.getElementById(label.htmlFor);
                        if (element && isInputElement(element)) return element;
                    }
                    
                    // 检查嵌套的input
                    element = label.querySelector('input, textarea, select');
                    if (element && isInputElement(element)) return element;
                }
            }
        }

        // 使用更通用的选择器
        for (const name of possibleNames) {
            const elements = document.querySelectorAll('input, textarea, select');
            for (const element of elements) {
                if (!isInputElement(element)) continue;
                
                // 检查是否在附近有相关文本
                const parent = element.parentElement;
                if (parent && parent.textContent.toLowerCase().includes(name.toLowerCase())) {
                    return element;
                }

                // 检查placeholder属性是否含有相关文本
                const placeholder = element.getAttribute('placeholder');
                if (placeholder && placeholder.toLowerCase().includes(name.toLowerCase())) {
                    return element;
                }
            }
        }

        return null;
    }

    // 判断是否是输入元素
    function isInputElement(element) {
        if (!element) return false;
        return element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT';
    }

    // 填充字段
    function fillField(field, value) {
        if (!field || !value) return;

        // 设置值
        field.value = value;
        
        // 触发事件，确保表单验证能够响应
        field.dispatchEvent(new Event('input', { bubbles: true }));
        field.dispatchEvent(new Event('change', { bubbles: true }));
        field.dispatchEvent(new Event('blur', { bubbles: true }));
    }

    // 从表单中读取当前值的函数
    function readFormData() {
        // 查找表单字段
        const usernameField = findFormField('username', 'user', 'login');
        const passwordField = findFormField('password', 'pass');
        const emailField = findFormField('email');
        const firstNameField = findFormField('first_name', 'firstname', 'fname', 'first name', 'name');
        const lastNameField = findFormField('last_name', 'lastname', 'lname', 'last name', 'surname');
        const addressLine1Field = findFormField('address_1', 'address1', 'address-line-1', 'address_line_1', 'address', 'street', 'line1');
        const addressLine2Field = findFormField('address_2', 'address2', 'address-line-2', 'address_line_2', 'apt', 'suite', 'line2');
        const cityField = findFormField('city', 'town');
        const stateField = findFormField('state', 'province', 'region');
        const postalCodeField = findFormField('zipcode', 'postal', 'zip', 'postcode');
        const phoneField = findFormField('phone_intl', 'phone', 'telephone', 'mobile', 'cell');

        // 读取字段值
        const formData = {
            username: usernameField ? usernameField.value : UI.generatedData.username,
            password: passwordField ? passwordField.value : UI.generatedData.password,
            email: emailField ? emailField.value : UI.userFilledEmail || UI.generatedData.email,
            firstName: firstNameField ? firstNameField.value : UI.generatedData.firstName,
            lastName: lastNameField ? lastNameField.value : UI.generatedData.lastName,
            addressLine1: addressLine1Field ? addressLine1Field.value : UI.generatedData.addressLine1,
            addressLine2: addressLine2Field ? addressLine2Field.value : UI.generatedData.addressLine2,
            city: cityField ? cityField.value : UI.generatedData.city,
            state: stateField ? stateField.value : UI.generatedData.state,
            postalCode: postalCodeField ? postalCodeField.value : UI.generatedData.postalCode,
            phone: phoneField ? phoneField.value : UI.generatedData.phone,
            timestamp: new Date().toLocaleString(),
            id: UI.generatedData.id || null  // 保留ID如果已存在
        };

        return formData;
    }

    // 添加动画样式
    function addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-10px); }
            }
        `;
        document.head.appendChild(style);
    }

    // 初始化时添加动画样式
    addStyles();

    // 初始化UI和数据
    UI.init();
})(); 
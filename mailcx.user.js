// ==UserScript==
// @name         MailCX 临时邮箱助手
// @namespace    http://tampermonkey.net/
// @version      0.7
// @description  MailCX临时邮箱API的油猴脚本实现，支持拖动按钮和查看邮件时间，可在任意网站使用
// <AUTHOR> 0.46.11
// @match        *://*/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_registerMenuCommand
// @connect      api.mail.cx
// ==/UserScript==

(function() {
    'use strict';

    // 用户配置
    const CONFIG = {
        // 是否在所有网站显示。设为 false 可以设置白名单
        showOnAllSites: GM_getValue('mailcx-show-all-sites', true),
        // 网站白名单，当 showOnAllSites 为 false 时生效
        siteWhitelist: GM_getValue('mailcx-whitelist', [
            'mail.cx',
            'namecheap.com'
        ]),
        // 默认按钮位置
        defaultPosition: {
            top: '33vh',
            right: '20px'
        },
        // 默认邮箱设置
        defaultEmail: GM_getValue('mailcx-default-email', ''),
        // 是否使用自定义邮箱
        useCustomEmail: GM_getValue('mailcx-use-custom-email', false)
    };

    // 保存配置
    function saveConfig() {
        GM_setValue('mailcx-show-all-sites', CONFIG.showOnAllSites);
        GM_setValue('mailcx-whitelist', CONFIG.siteWhitelist);
        GM_setValue('mailcx-default-email', CONFIG.defaultEmail);
        GM_setValue('mailcx-use-custom-email', CONFIG.useCustomEmail);
    }

    // 显示设置对话框
    function showSettings() {
        // 先检查页面上是否已存在设置对话框，如存在则先移除
        const existingSettings = document.querySelector('.mailcx-settings');
        if (existingSettings) {
            existingSettings.remove();
        }
        
        // 创建设置对话框
        const settingsModal = document.createElement('div');
        settingsModal.className = 'mailcx-settings';
        settingsModal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
            z-index: 10001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        
        settingsModal.innerHTML = `
            <h3 style="margin-top:0; color: #333;">邮件助手设置</h3>
            <div style="margin-bottom: 15px;">
                <label style="color: #333; display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="showAllSites" ${CONFIG.showOnAllSites ? 'checked' : ''}>
                    在所有网站上显示
                </label>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="color: #333; display: block; margin-bottom: 5px;">网站白名单（当不在所有网站显示时生效）：</label>
                <p style="font-size: 12px; color: #666; margin-bottom: 5px;">每行一个网站域名，无需 http:// 或 https://</p>
                <textarea id="whitelist" style="width: 100%; height: 100px; padding: 8px; box-sizing: border-box; background-color: white; color: #333; border: 1px solid #ddd; border-radius: 4px;">${CONFIG.siteWhitelist.join('\n')}</textarea>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="color: #333; display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="useCustomEmail" ${CONFIG.useCustomEmail ? 'checked' : ''}>
                    使用默认邮箱
                </label>
                <p style="font-size: 12px; color: #666; margin-bottom: 5px;">启用后将自动填充设置的默认邮箱地址</p>
                <input type="text" id="defaultEmail" class="mailcx-input" style="margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; background-color: white !important; color: #333 !important;" value="${CONFIG.defaultEmail}" placeholder="输入默认邮箱地址">
            </div>
            <div style="display: flex; justify-content: space-between;">
                <button id="saveSettings" style="
                    padding: 8px 16px;
                    background: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                ">保存</button>
                <button id="cancelSettings" style="
                    padding: 8px 16px;
                    background: #f44336;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                ">取消</button>
            </div>
        `;
        
        document.body.appendChild(settingsModal);
        
        // 绑定事件
        document.getElementById('saveSettings').addEventListener('click', function() {
            CONFIG.showOnAllSites = document.getElementById('showAllSites').checked;
            CONFIG.siteWhitelist = document.getElementById('whitelist').value
                .split('\n')
                .map(site => site.trim())
                .filter(site => site.length > 0);
            
            CONFIG.defaultEmail = document.getElementById('defaultEmail').value.trim();
            CONFIG.useCustomEmail = document.getElementById('useCustomEmail').checked;
            
            saveConfig();
            settingsModal.remove();
            
            // 提示用户刷新页面
            alert('设置已保存。请刷新页面使更改生效。');
        });
        
        document.getElementById('cancelSettings').addEventListener('click', function() {
            settingsModal.remove();
        });
    }
    
    // 注册菜单命令
    GM_registerMenuCommand('MailCX 助手设置', showSettings);

    // 检查当前网站是否应该显示邮件助手
    function shouldShowOnCurrentSite() {
        if (CONFIG.showOnAllSites) return true;
        
        const currentHost = window.location.hostname;
        return CONFIG.siteWhitelist.some(site => currentHost.includes(site));
    }

    class MailCXError extends Error {
        constructor(message) {
            super(message);
            this.name = 'MailCXError';
        }
    }

    class MailCX {
        constructor() {
            this.BASE_URL = 'https://api.mail.cx/api/v1';
            this.token = null;
        }

        async makeRequest(method, endpoint, options = {}) {
            const url = `${this.BASE_URL}${endpoint}`;
            const headers = {
                'accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ...(this.token ? {'Authorization': `Bearer ${this.token}`} : {}),
                ...options.headers
            };

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: method,
                    url: url,
                    headers: headers,
                    data: options.body,
                    timeout: 30000,
                    onload: function(response) {
                        if (response.status >= 200 && response.status < 300) {
                            try {
                                const result = response.responseText ? JSON.parse(response.responseText) : {};
                                resolve(result);
                            } catch (e) {
                                resolve(response.responseText);
                            }
                        } else {
                            reject(new MailCXError(`请求失败: ${response.status} ${response.statusText}`));
                        }
                    },
                    onerror: function(error) {
                        reject(new MailCXError(`请求错误: ${error}`));
                    }
                });
            });
        }

        async authorize() {
            try {
                const response = await this.makeRequest('POST', '/auth/authorize_token', {
                    headers: {'Authorization': 'Bearer undefined'}
                });
                if (typeof response === 'string') {
                    this.token = response.replace(/"/g, '');
                } else {
                    throw new MailCXError('无法获取token');
                }
            } catch (e) {
                console.error('认证失败:', e);
                throw e;
            }
        }

        async getMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}`);
        }

        async getMessage(email, messageId) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMessage(email, messageId) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}`);
        }
    }

    // 创建样式
    function createStyles() {
        const styles = `
            /* 全局重置，确保在任何网站上样式一致 */
            .mailcx-modal *,
            .mailcx-settings * {
                all: initial !important;
                box-sizing: border-box !important;
                font-family: Arial, sans-serif !important;
                line-height: normal !important;
                color-scheme: light !important;
            }
            
            .mailcx-trigger-btn {
                position: fixed;
                top: 33vh;
                right: 20px;
                padding: 10px 20px;
                background: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                z-index: 9998;
                font-size: 14px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                user-select: none;
            }
            
            .mailcx-trigger-btn.dragging {
                cursor: move;
                opacity: 0.8;
            }

            .mailcx-trigger-btn:hover {
                background: #45a049;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            /* 标题栏图标样式 */
            .mailcx-action-icon {
                cursor: pointer;
                transition: transform 0.2s ease;
            }
            
            .mailcx-action-icon:hover {
                transform: scale(1.2);
            }
            
            /* 刷新按钮动画 */
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            .mailcx-action-icon.refreshing svg {
                animation: rotate 1s linear infinite;
            }
            
            .mailcx-action-icon.refreshing {
                color: #1565C0 !important;
            }
            
            /* 加载中状态提示 */
            .mailcx-loading {
                text-align: center;
                padding: 20px;
                color: #666;
                font-size: 14px;
            }
            
            /* 全局样式，适用于所有元素 */
            .mailcx-modal *,
            .mailcx-trigger-btn,
            .mailcx-settings * {
                color-scheme: light !important; /* 强制使用亮色主题 */
            }
            
            /* 确保下拉菜单在黑暗主题下正常显示 */
            .mailcx-modal select,
            .mailcx-modal select option {
                background-color: white !important;
                color: #333 !important;
            }
            
            .mailcx-modal select#domainSelect {
                background-color: #1976D2 !important;
                color: white !important;
            }
            
            .mailcx-modal select#domainSelect option {
                background-color: white !important;
                color: #333 !important;
            }

            .mailcx-modal {
                display: none;
                position: fixed;
                top: calc(33vh + 50px);
                right: 20px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.15);
                z-index: 9999;
                width: 400px;
                max-height: 600px;
                overflow: hidden;
            }

            .mailcx-modal-content {
                width: 100%;
                height: 100%;
                padding: 15px;
                overflow-y: auto;
                max-height: 600px;
                box-sizing: border-box;
                background-color: white;
                color: #333;
            }

            .mailcx-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 15px;
                background: #f8f8f8;
                border-bottom: 1px solid #eee;
                cursor: move;
                user-select: none;
            }

            .mailcx-modal-title {
                font-weight: bold;
                font-size: 16px;
                color: #333;
            }

            .mailcx-close {
                cursor: pointer;
                color: #666;
                font-size: 18px;
                padding: 5px;
            }

            .mailcx-close:hover {
                color: #333;
            }

            .mailcx-header {
                margin-bottom: 15px;
            }

            .mailcx-input {
                width: 100% !important;
                padding: 8px 12px !important;
                margin-bottom: 10px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                background-color: white !important;
                color: #333 !important;
                box-sizing: border-box !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                line-height: normal !important;
                height: auto !important;
                min-height: 36px !important;
                outline: none !important;
                text-shadow: none !important;
                appearance: none !important;
                -webkit-appearance: none !important;
                transition: border-color 0.2s ease !important;
                position: relative !important;
                display: block !important;
                opacity: 1 !important;
                z-index: 1 !important;
            }

            .mailcx-input:focus {
                border-color: #2196F3 !important;
                box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2) !important;
            }

            .mailcx-button {
                padding: 8px 16px;
                margin-right: 10px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s ease;
            }

            .mailcx-button:hover {
                opacity: 0.9;
            }

            .mailcx-button-primary {
                background: #4CAF50;
                color: white;
            }

            .mailcx-button-danger {
                background: #f44336;
                color: white;
            }

            .mailcx-message {
                padding: 10px !important;
                border-bottom: 1px solid #eee !important;
                background-color: white !important;
                color: #333 !important;
                margin: 0 !important;
                position: relative !important;
                display: block !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                line-height: normal !important;
                text-align: left !important;
            }

            .mailcx-message-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                gap: 10px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .mailcx-message-subject {
                flex-grow: 2 !important;
                cursor: pointer !important;
                padding: 5px !important;
                border-radius: 4px !important;
                transition: background-color 0.2s ease !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                color: #333 !important;
                max-width: 60% !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                font-weight: normal !important;
                text-decoration: none !important;
                background: transparent !important;
                border: none !important;
                text-align: left !important;
            }

            .mailcx-message-subject:hover {
                background-color: #f5f5f5;
            }

            .mailcx-message-time {
                font-size: 12px !important;
                color: #666 !important;
                flex-grow: 1 !important;
                text-align: right !important;
                white-space: nowrap !important;
                padding-right: 10px !important;
                font-family: Arial, sans-serif !important;
                line-height: normal !important;
                background: transparent !important;
            }

            .mailcx-message-actions {
                display: flex;
                gap: 10px;
                white-space: nowrap;
            }

            .mailcx-delete-btn {
                padding: 4px 8px !important;
                background: #f44336 !important;
                color: white !important;
                border: none !important;
                border-radius: 4px !important;
                cursor: pointer !important;
                font-size: 12px !important;
                transition: all 0.2s ease !important;
                font-family: Arial, sans-serif !important;
                min-width: 40px !important;
                text-align: center !important;
                line-height: normal !important;
                margin: 0 !important;
                display: inline-block !important;
            }

            .mailcx-delete-btn:hover {
                background: #d32f2f;
            }
        `;
        GM_addStyle(styles);
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return dateString; // 如果解析失败，返回原始字符串
            }
            
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        } catch (e) {
            return dateString;
        }
    }

    // 防止元素被拖出屏幕范围
    function keepInScreen(element) {
        const rect = element.getBoundingClientRect();
        const viewWidth = window.innerWidth;
        const viewHeight = window.innerHeight;
        
        // 检查是否超出左边界
        if (rect.left < 0) {
            element.style.left = '0px';
        }
        
        // 检查是否超出右边界
        if (rect.right > viewWidth) {
            element.style.left = (viewWidth - rect.width) + 'px';
        }
        
        // 检查是否超出上边界
        if (rect.top < 0) {
            element.style.top = '0px';
        }
        
        // 检查是否超出下边界
        if (rect.bottom > viewHeight) {
            element.style.top = (viewHeight - rect.height) + 'px';
        }
    }

    // 统一的拖动处理函数
    function makeElementDraggable(element, handle, savePositionCallback) {
        let isDragging = false;
        let mouseOffset = { x: 0, y: 0 };
        let dragStartTime = 0;
        let startPosition = { x: 0, y: 0 };
        let hasMoved = false;
        const DRAG_THRESHOLD = 5; // 像素
        
        if (!handle) {
            handle = element;
        }
        
        handle.addEventListener('mousedown', function(e) {
            // 确保在按钮本身上按下时才开始拖动
            if (e.target !== element && e.target !== handle && !handle.contains(e.target)) {
                return;
            }
            
            e.preventDefault();
            e.stopPropagation();
            
            const rect = element.getBoundingClientRect();
            mouseOffset.x = e.clientX - rect.left;
            mouseOffset.y = e.clientY - rect.top;
            startPosition.x = e.clientX;
            startPosition.y = e.clientY;
            
            isDragging = true;
            hasMoved = false;
            dragStartTime = Date.now();
            
            // 添加拖动中状态样式
            element.classList.add('dragging');
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            // 计算移动距离
            const dx = Math.abs(e.clientX - startPosition.x);
            const dy = Math.abs(e.clientY - startPosition.y);
            
            // 只有移动超过阈值才认为是拖动
            if (dx > DRAG_THRESHOLD || dy > DRAG_THRESHOLD) {
                hasMoved = true;
                
                // 使用视窗坐标设置位置
                const left = e.clientX - mouseOffset.x;
                const top = e.clientY - mouseOffset.y;
                
                // 确保不会超出屏幕
                element.style.left = Math.max(0, left) + 'px';
                element.style.top = Math.max(0, top) + 'px';
                element.style.right = 'auto';
                element.style.bottom = 'auto';
                
                // 防止拖出右边和底部
                const rect = element.getBoundingClientRect();
                if (rect.right > window.innerWidth) {
                    element.style.left = (window.innerWidth - rect.width) + 'px';
                }
                if (rect.bottom > window.innerHeight) {
                    element.style.top = (window.innerHeight - rect.height) + 'px';
                }
            }
        });
        
        document.addEventListener('mouseup', function() {
            if (!isDragging) return;
            
            const dragDuration = Date.now() - dragStartTime;
            const wasDragged = hasMoved && dragDuration > 100;
            
            element.classList.remove('dragging');
            isDragging = false;
            
            if (wasDragged && savePositionCallback) {
                savePositionCallback(element);
            }
        });
        
        return {
            isDragging: function() {
                return isDragging;
            },
            wasDragged: function() {
                return hasMoved;
            }
        };
    }

    // 创建UI元素
    function createUI() {
        createStyles();

        // 创建触发按钮
        const triggerButton = document.createElement('button');
        triggerButton.textContent = 'Mail.cx 邮件助手';
        triggerButton.className = 'mailcx-trigger-btn';
        triggerButton.title = '点击打开邮件管理，右键点击进行设置';
        document.body.appendChild(triggerButton);

        // 从存储中获取按钮位置
        const savedBtnPos = GM_getValue('mailcx-btn-pos');
        if (savedBtnPos) {
            // 确保恢复的位置在视窗内
            const viewWidth = window.innerWidth;
            const viewHeight = window.innerHeight;
            
            // 计算按钮的宽高 (预估值，实际加载后可能不同)
            const btnWidth = 120;
            const btnHeight = 40;
            
            const left = Math.min(Math.max(0, savedBtnPos.left), viewWidth - btnWidth);
            const top = Math.min(Math.max(0, savedBtnPos.top), viewHeight - btnHeight);
            
            triggerButton.style.left = left + 'px';
            triggerButton.style.top = top + 'px';
            triggerButton.style.right = 'auto';
        } else {
            // 使用默认位置
            triggerButton.style.top = CONFIG.defaultPosition.top;
            triggerButton.style.right = CONFIG.defaultPosition.right;
        }

        // 创建模态窗口
        const modal = document.createElement('div');
        modal.className = 'mailcx-modal';
        modal.innerHTML = `
            <div class="mailcx-modal-header">
                <div class="mailcx-modal-title">邮件管理</div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <span class="mailcx-action-icon" id="refreshIcon" title="刷新邮件" style="color: #4CAF50; font-size: 18px; display: flex; align-items: center; justify-content: center; width: 24px; height: 24px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="1 4 1 10 7 10"></polyline>
                            <polyline points="23 20 23 14 17 14"></polyline>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                        </svg>
                    </span>
                    <span class="mailcx-action-icon" id="clearIcon" title="清空邮箱" style="color: #f44336; font-size: 18px; display: flex; align-items: center; justify-content: center; width: 24px; height: 24px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                    </span>
                    <span class="mailcx-close" style="font-size: 20px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">&times;</span>
                </div>
            </div>
            <div class="mailcx-modal-content">
                <div class="mailcx-header">
                    <div style="margin-bottom: 10px; padding: 0 5px;">
                        <input type="text" class="mailcx-input" id="emailInput" placeholder="输入邮箱地址" style="
                            width: 100% !important;
                            padding: 8px 12px !important;
                            margin: 0 0 10px 0 !important;
                            border: 1px solid #ddd !important;
                            border-radius: 4px !important;
                            background-color: white !important;
                            color: #333 !important;
                            box-sizing: border-box !important;
                            font-family: Arial, sans-serif !important;
                            font-size: 14px !important;
                            line-height: normal !important;
                            height: auto !important;
                            min-height: 36px !important;
                            outline: none !important;
                            text-shadow: none !important;
                            appearance: none !important;
                            -webkit-appearance: none !important;
                            transition: border-color 0.2s ease !important;
                            position: relative !important;
                            display: block !important;
                            opacity: 1 !important;
                            z-index: 1 !important;
                            max-width: 100% !important;
                            text-align: left !important;
                            text-transform: none !important;
                            letter-spacing: normal !important;
                            word-spacing: normal !important;
                            text-indent: 0 !important;
                            text-rendering: auto !important;
                            background-image: none !important;
                            resize: none !important;
                            box-shadow: none !important;
                            vertical-align: baseline !important;
                        ">
                        <div id="emailTypeHint" style="font-size: 12px; color: #666; margin-top: 5px; text-align: right;"></div>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 8px; padding: 0 5px;">
                        <div style="display: flex; justify-content: space-between; gap: 8px;">
                            <button class="mailcx-button" id="defaultEmailBtn" style="background-color: #4CAF50; color: white; width: calc(50% - 4px); height: 36px;" ${CONFIG.useCustomEmail && CONFIG.defaultEmail ? '' : 'disabled'}>默认邮箱</button>
                            <div style="display: flex; width: calc(50% - 4px);">
                                <button class="mailcx-button" id="randomEmailBtn" style="background-color: #2196F3; color: white; border-top-right-radius: 0; border-bottom-right-radius: 0; margin-right: 0; width: calc(100% - 90px); height: 36px;">随机</button>
                                <select id="domainSelect" style="width: 90px; border: none; background-color: #1976D2; color: white !important; border-top-right-radius: 4px; border-bottom-right-radius: 4px; cursor: pointer; padding: 0 5px; height: 36px;">
                                    <option value="qabq.com" style="background-color: white; color: #333;">@qabq.com</option>
                                    <option value="nqmo.com" style="background-color: white; color: #333;">@nqmo.com</option>
                                    <option value="end.tw" style="background-color: white; color: #333;">@end.tw</option>
                                    <option value="uuf.me" selected style="background-color: white; color: #333;">@uuf.me</option>
                                    <option value="yzm.de" style="background-color: white; color: #333;">@yzm.de</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="messagesList"></div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // 计算按钮位置并更新
        setTimeout(() => {
            // 如果没有保存的位置，确保按钮在视窗内
            if (!savedBtnPos) {
                const rect = triggerButton.getBoundingClientRect();
                if (rect.right > window.innerWidth) {
                    triggerButton.style.left = (window.innerWidth - rect.width) + 'px';
                    triggerButton.style.right = 'auto';
                }
            }
        }, 100);

        // 设置按钮可拖动
        const buttonDrag = makeElementDraggable(triggerButton, triggerButton, (el) => {
            GM_setValue('mailcx-btn-pos', {
                left: parseFloat(el.style.left),
                top: parseFloat(el.style.top)
            });
        });

        // 设置模态框可拖动
        const modalDrag = makeElementDraggable(modal, modal.querySelector('.mailcx-modal-header'));

        // 初始化MailCX客户端
        const mailClient = new MailCX();
        
        // 鼠标点击时间变量
        let lastMouseUpTime = 0;
        let lastWasDragged = false;

        // 获取邮箱输入框和相关按钮
        const emailInput = modal.querySelector('#emailInput');
        const messagesList = modal.querySelector('#messagesList');
        const refreshIcon = modal.querySelector('#refreshIcon');
        const clearIcon = modal.querySelector('#clearIcon');
        const randomEmailBtn = modal.querySelector('#randomEmailBtn');
        const emailTypeHint = modal.querySelector('#emailTypeHint');
        const domainSelect = modal.querySelector('#domainSelect');
        const defaultEmailBtn = modal.querySelector('#defaultEmailBtn');

        // 更新邮箱类型提示
        function updateEmailTypeHint() {
            if (CONFIG.useCustomEmail && CONFIG.defaultEmail && emailInput.value === CONFIG.defaultEmail) {
                emailTypeHint.textContent = "当前使用: 默认邮箱";
                emailTypeHint.style.color = "#4CAF50";
            } else if (emailInput.value.includes('@mail.cx') || 
                       emailInput.value.includes('@qabq.com') ||
                       emailInput.value.includes('@nqmo.com') ||
                       emailInput.value.includes('@end.tw') ||
                       emailInput.value.includes('@uuf.me') ||
                       emailInput.value.includes('@yzm.de')) {
                emailTypeHint.textContent = "当前使用: 随机邮箱";
                emailTypeHint.style.color = "#2196F3";
            } else if (emailInput.value) {
                emailTypeHint.textContent = "当前使用: 自定义邮箱";
                emailTypeHint.style.color = "#FFA000";
            } else {
                emailTypeHint.textContent = "";
            }
            
            // 根据是否有默认邮箱来禁用或启用默认邮箱按钮
            if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
                defaultEmailBtn.disabled = false;
                defaultEmailBtn.style.opacity = "1";
            } else {
                defaultEmailBtn.disabled = true;
                defaultEmailBtn.style.opacity = "0.5";
            }
        }

        // 如果设置了默认邮箱，自动填充
        if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
            emailInput.value = CONFIG.defaultEmail;
            updateEmailTypeHint();
        }

        // 监听输入框变化，更新提示
        emailInput.addEventListener('input', updateEmailTypeHint);
        
        // 使用默认邮箱按钮事件
        defaultEmailBtn.addEventListener('click', () => {
            if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
                emailInput.value = CONFIG.defaultEmail;
                updateEmailTypeHint();
            } else {
                alert('请先在设置中配置默认邮箱');
            }
        });

        // 生成随机邮箱
        function generateRandomEmail() {
            const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
            const selectedDomain = domainSelect.value;
            
            let randomString = '';
            for (let i = 0; i < 8; i++) {
                randomString += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            
            return randomString + '@' + selectedDomain;
        }

        // 绑定随机邮箱按钮事件
        randomEmailBtn.addEventListener('click', () => {
            emailInput.value = generateRandomEmail();
            updateEmailTypeHint();
            
            // 顺便同步一下域名菜单中的选项
            const atIndex = emailInput.value.indexOf('@');
            if (atIndex > 0) {
                const domain = emailInput.value.substring(atIndex + 1);
                for (let i = 0; i < domainSelect.options.length; i++) {
                    if (domainSelect.options[i].value === domain) {
                        domainSelect.selectedIndex = i;
                        break;
                    }
                }
            }
        });
        
        // 当选择域名时，如果邮箱是随机生成的，则更新域名
        domainSelect.addEventListener('change', () => {
            const emailValue = emailInput.value;
            const atIndex = emailValue.indexOf('@');
            
            if (atIndex > 0 && emailTypeHint.textContent.includes('随机邮箱')) {
                // 只保留@前面的部分，添加新域名
                emailInput.value = emailValue.substring(0, atIndex) + '@' + domainSelect.value;
                updateEmailTypeHint();
            }
        });

        // 事件处理
        triggerButton.addEventListener('mouseup', function() {
            lastMouseUpTime = Date.now();
            lastWasDragged = buttonDrag.wasDragged();
        });
        
        triggerButton.addEventListener('click', function(e) {
            // 如果是拖动结束，不触发点击事件
            if (lastWasDragged && Date.now() - lastMouseUpTime < 200) {
                e.stopPropagation();
                return;
            }
            
            // 切换显示/隐藏
            if (modal.style.display === 'none' || !modal.style.display) {
                // 首次显示时，将位置设置在按钮附近
                const btnRect = triggerButton.getBoundingClientRect();
                
                // 确保弹窗不会超出屏幕
                let leftPos = btnRect.left;
                if (leftPos + 400 > window.innerWidth) {
                    leftPos = window.innerWidth - 400;
                }
                
                // 检查垂直方向是否有足够空间向下弹出
                // 假设弹窗高度约为500px
                const modalHeight = 500;
                const isBottomOverflow = btnRect.bottom + 10 + modalHeight > window.innerHeight;
                
                if (isBottomOverflow) {
                    // 向上弹出
                    modal.style.top = 'auto';
                    modal.style.bottom = (window.innerHeight - btnRect.top + 10) + 'px';
                } else {
                    // 向下弹出
                    modal.style.top = (btnRect.bottom + 10) + 'px';
                    modal.style.bottom = 'auto';
                }
                
                modal.style.left = leftPos + 'px';
                modal.style.right = 'auto';
                
                modal.style.display = 'block';
            } else {
                modal.style.display = 'none';
            }
            
            e.stopPropagation();
        });
        
        // 添加右键菜单处理
        triggerButton.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            // 关闭邮件管理界面再打开设置界面
            modal.style.display = 'none';
            showSettings();
            return false;
        });

        modal.querySelector('.mailcx-close').addEventListener('click', () => {
            modal.style.display = 'none';
        });

        async function refreshMessages() {
            const email = emailInput.value.trim();
            if (!email) {
                alert('请输入邮箱地址');
                return;
            }
            
            // 添加刷新动画
            refreshIcon.classList.add('refreshing');
            
            // 显示加载状态
            messagesList.innerHTML = '<div class="mailcx-loading">正在加载邮件列表...</div>';

            try {
                const messages = await mailClient.getMailbox(email);
                messagesList.innerHTML = '';

                if (Array.isArray(messages) && messages.length > 0) {
                    for (const msg of messages) {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'mailcx-message';
                        messageDiv.style.cssText = `
                            padding: 10px !important;
                            border-bottom: 1px solid #eee !important;
                            background-color: white !important;
                            color: #333 !important;
                            margin: 0 !important;
                            position: relative !important;
                            display: block !important;
                            font-family: Arial, sans-serif !important;
                            font-size: 14px !important;
                            line-height: normal !important;
                            text-align: left !important;
                        `;
                        
                        const headerDiv = document.createElement('div');
                        headerDiv.className = 'mailcx-message-header';
                        headerDiv.style.cssText = `
                            display: flex !important;
                            justify-content: space-between !important;
                            align-items: center !important;
                            gap: 10px !important;
                            width: 100% !important;
                            box-sizing: border-box !important;
                        `;
                        
                        const subjectDiv = document.createElement('div');
                        subjectDiv.className = 'mailcx-message-subject';
                        subjectDiv.textContent = msg.subject || '(无主题)';
                        subjectDiv.style.cssText = `
                            flex-grow: 2 !important;
                            cursor: pointer !important;
                            padding: 5px !important;
                            border-radius: 4px !important;
                            transition: background-color 0.2s ease !important;
                            white-space: nowrap !important;
                            overflow: hidden !important;
                            text-overflow: ellipsis !important;
                            color: #333 !important;
                            max-width: 60% !important;
                            font-family: Arial, sans-serif !important;
                            font-size: 14px !important;
                            font-weight: normal !important;
                            text-decoration: none !important;
                            background: transparent !important;
                            border: none !important;
                            text-align: left !important;
                        `;
                        
                        // 时间显示在同一行
                        const timeDiv = document.createElement('div');
                        timeDiv.className = 'mailcx-message-time';
                        timeDiv.textContent = msg.date ? formatDateTime(msg.date) : '未知时间';
                        timeDiv.style.cssText = `
                            font-size: 12px !important;
                            color: #666 !important;
                            flex-grow: 1 !important;
                            text-align: right !important;
                            white-space: nowrap !important;
                            padding-right: 10px !important;
                            font-family: Arial, sans-serif !important;
                            line-height: normal !important;
                            background: transparent !important;
                        `;
                        
                        const actionsDiv = document.createElement('div');
                        actionsDiv.className = 'mailcx-message-actions';
                        actionsDiv.style.cssText = `
                            display: flex !important;
                            gap: 10px !important;
                            white-space: nowrap !important;
                        `;
                        
                        const deleteBtn = document.createElement('button');
                        deleteBtn.className = 'mailcx-delete-btn';
                        deleteBtn.textContent = '删除';
                        deleteBtn.style.cssText = `
                            padding: 4px 8px !important;
                            background: #f44336 !important;
                            color: white !important;
                            border: none !important;
                            border-radius: 4px !important;
                            cursor: pointer !important;
                            font-size: 12px !important;
                            transition: all 0.2s ease !important;
                            font-family: Arial, sans-serif !important;
                            min-width: 40px !important;
                            text-align: center !important;
                            line-height: normal !important;
                            margin: 0 !important;
                            display: inline-block !important;
                        `;
                        
                        actionsDiv.appendChild(deleteBtn);
                        
                        // 更改DOM结构，将时间显示放在主题后面
                        headerDiv.appendChild(subjectDiv);
                        headerDiv.appendChild(timeDiv);
                        headerDiv.appendChild(actionsDiv);
                        
                        messageDiv.appendChild(headerDiv);
                        messagesList.appendChild(messageDiv);

                        // 查看邮件内容
                        subjectDiv.addEventListener('click', async () => {
                            try {
                                const detail = await mailClient.getMessage(email, msg.id);
                                const content = detail.body?.text || detail.body?.html || '无内容';
                                // 创建一个更好的内容显示弹窗
                                const contentModal = document.createElement('div');
                                contentModal.style.cssText = `
                                    position: fixed;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                    background: white;
                                    padding: 20px;
                                    border-radius: 8px;
                                    max-width: 80%;
                                    max-height: 80%;
                                    overflow: auto;
                                    z-index: 10000;
                                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                                `;
                                contentModal.innerHTML = `
                                    <div style="margin-bottom: 10px;">
                                        <strong>主题：</strong>${msg.subject || '(无主题)'}
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <strong>时间：</strong>${msg.date ? formatDateTime(msg.date) : '未知时间'}
                                    </div>
                                    <div style="white-space: pre-wrap;">${content}</div>
                                    <button style="
                                        position: absolute;
                                        top: 10px;
                                        right: 10px;
                                        padding: 5px 10px;
                                        background: #666;
                                        color: white;
                                        border: none;
                                        border-radius: 4px;
                                        cursor: pointer;
                                    ">关闭</button>
                                `;
                                document.body.appendChild(contentModal);
                                
                                contentModal.querySelector('button').onclick = () => {
                                    contentModal.remove();
                                };
                            } catch (e) {
                                console.error('获取邮件详情失败:', e);
                                alert('获取邮件详情失败');
                            }
                        });

                        // 设置主题鼠标悬停效果
                        subjectDiv.addEventListener('mouseover', () => {
                            subjectDiv.style.backgroundColor = '#f5f5f5 !important';
                        });
                        
                        subjectDiv.addEventListener('mouseout', () => {
                            subjectDiv.style.backgroundColor = 'transparent !important';
                        });

                        // 设置删除按钮鼠标悬停效果
                        deleteBtn.addEventListener('mouseover', () => {
                            deleteBtn.style.backgroundColor = '#d32f2f !important';
                        });
                        
                        deleteBtn.addEventListener('mouseout', () => {
                            deleteBtn.style.backgroundColor = '#f44336 !important';
                        });

                        // 删除邮件
                        deleteBtn.addEventListener('click', async () => {
                            try {
                                await mailClient.deleteMessage(email, msg.id);
                                messageDiv.remove();
                            } catch (e) {
                                console.error('删除邮件失败:', e);
                                alert('删除邮件失败');
                            }
                        });
                    }
                } else {
                    messagesList.innerHTML = '<div style="padding: 10px; text-align: center;">未找到邮件</div>';
                }
            } catch (e) {
                console.error('获取邮件列表失败:', e);
                messagesList.innerHTML = `<div style="padding: 10px; color: #f44336; text-align: center;">获取邮件失败: ${e.message || '未知错误'}</div>`;
            } finally {
                // 无论成功或失败，都移除刷新动画
                refreshIcon.classList.remove('refreshing');
            }
        }

        // 确保正确绑定刷新按钮事件
        refreshIcon.addEventListener('click', function() {
            console.log('刷新按钮被点击');
            refreshMessages();
        });

        clearIcon.addEventListener('click', async () => {
            const email = emailInput.value.trim();
            if (!email) {
                alert('请输入邮箱地址');
                return;
            }

            if (confirm('确定要清空邮箱吗？此操作不可恢复！')) {
                // 添加删除动画
                clearIcon.style.color = '#d32f2f';
                
                try {
                    await mailClient.deleteMailbox(email);
                    messagesList.innerHTML = '<div style="padding: 10px; text-align: center;">邮箱已清空</div>';
                } catch (e) {
                    console.error('清空邮箱失败:', e);
                    alert('清空邮箱失败');
                } finally {
                    // 恢复颜色
                    setTimeout(() => {
                        clearIcon.style.color = '#f44336';
                    }, 500);
                }
            }
        });
    }

    // 页面加载完成后创建UI
    window.addEventListener('load', function() {
        // 检查当前网站是否应该显示邮件助手
        if (shouldShowOnCurrentSite()) {
            createUI();
        }
    });
})(); 
# Cloudflare Email Routing集成升级计划

## 项目背景
将`cursor_reg_longtoken.py`中的邮件接收和验证码处理逻辑转换为JavaScript，集成到`augmentcode_token_helper_enhanced.user.js`中，支持Cloudflare Email Routing的catch-all转发功能。

## 核心方案
在现有半自动框架基础上，添加双邮箱配置和MailCX API集成：
- **前端邮箱**：自定义后缀域名邮箱（用于注册）
- **接收邮箱**：固定的@uuf.me邮箱（用于API获取验证码）

## 工作流程
1. 用户在设置中配置自定义后缀域名和@uuf.me接收邮箱
2. 点击"生成邮箱"创建前端邮箱（随机前缀@自定义后缀）
3. 填充前端邮箱到注册页面
4. 注册页面发送验证邮件到前端邮箱
5. Cloudflare自动转发到@uuf.me接收邮箱
6. 点击"获取验证码"按钮，脚本通过API获取验证码
7. 自动填充验证码到页面

## 详细执行步骤

### 第一阶段：设置界面扩展
- 添加"设置"标签页
- 自定义后缀域名输入框
- @uuf.me接收邮箱输入框
- localStorage保存设置

### 第二阶段：MailCX API客户端集成
- 移植Python中的MailCX API调用逻辑
- token认证、获取邮件、删除邮件功能
- GM_xmlhttpRequest跨域请求支持

### 第三阶段：邮箱生成逻辑升级
- 支持自定义后缀域名
- 格式：随机前缀@自定义后缀域名
- 设置验证功能

### 第四阶段：验证码自动获取功能
- "获取验证码"按钮（手动触发）
- 邮件轮询逻辑
- 验证码提取正则表达式
- 自动填充功能

### 第五阶段：UI界面优化
- 双邮箱状态显示
- 更新使用说明
- 优化操作流程

### 第六阶段：事件处理和状态管理
- 设置保存/加载
- 验证码获取事件处理
- 状态显示逻辑更新

## 新增配置项
```javascript
const CONFIG = {
    customDomain: '',        // 自定义后缀域名
    receiverEmail: '',       // @uuf.me接收邮箱
    pollInterval: 5000,      // 邮件轮询间隔(毫秒)
    maxPollAttempts: 12      // 最大轮询次数(1分钟)
};
```

## 保持现有特性
- Vue.js + jQuery技术栈
- 拖拽功能
- 跨网站兼容性
- 手动复制/填充备用方案
- 数据清除功能

## 技术要点
1. 保持半自动特性（手动触发验证码获取）
2. 集成MailCX API（与Python版本等效）
3. 支持Cloudflare Email Routing转发
4. 完整的错误处理和状态提示
5. 跨网站兼容性保持

## 风险控制
- 保留原有手动输入验证码备用方案
- 完整的错误日志和状态提示
- 设置验证和默认值处理

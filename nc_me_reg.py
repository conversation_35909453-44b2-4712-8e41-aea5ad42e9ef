from math import log
import random
import string
import time
import os
import json
import logging
from datetime import datetime
from DrissionPage import Chromium, ChromiumOptions
from colorama import init, Fore, Style
import re
import requests

# 初始化colorama
init()

# 配置logger
logger = logging.getLogger('nc_me_reg')
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件处理器
file_handler = logging.FileHandler('nc_me_reg.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# 域名和URL读取工具类
class DomainReader:
    @staticmethod
    def read_domains_file(filename="domains.txt"):
        """读取域名文件，返回域名和URL的列表"""
        domains_data = []
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    for line in f:
                        # 使用正则表达式匹配连续的连字符（至少1个）作为分隔符
                        parts = re.split(r'-+', line.strip())
                        if len(parts) >= 2:
                            # 如果分割后有多于2部分，说明域名或URL中可能包含"-"
                            # 取第一部分作为域名，其余部分拼接为URL
                            domain = parts[0]
                            url = ''.join(parts[1:])
                            domains_data.append({
                                "domain": domain,
                                "url": url
                            })
                logger.info(f"从 {filename} 读取了 {len(domains_data)} 个域名")
                return domains_data
            else:
                logger.warning(f"文件 {filename} 不存在")
                return []
        except Exception as e:
            logger.error(f"读取域名文件失败: {e}")
            return []

# 数据生成工具类
class DataGenerator:
    @staticmethod
    def random_element(array):
        """从数组中随机选择一个元素"""
        return random.choice(array)
    
    @staticmethod
    def random_string(length, chars=None):
        """生成随机字符串"""
        if chars is None:
            chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def get_password(length=10, include_special=True):
        """生成随机密码"""
        chars = string.ascii_letters + string.digits
        if include_special:
            chars += "!@#$%^&*()_+~`|}{[]\\:;?><,./-="
        return DataGenerator.random_string(length, chars)
    
    @staticmethod
    def generate_person_data(domain=None):
        """使用randomuser.me API生成完整个人信息数据集"""
        try:
            # 指定使用本地代理127.0.0.1:10809
            proxies = {
                'http': 'http://127.0.0.1:10809',
                'https': 'http://127.0.0.1:10809'
            }
            response = requests.get("https://randomuser.me/api/?nat=us", proxies=proxies)
            if response.status_code != 200:
                logger.error(f"获取随机用户数据失败: 状态码 {response.status_code}")
                raise Exception(f"API响应错误: {response.status_code}")
                
            data = response.json()["results"][0]
                        
            # 使用本地逻辑生成密码
            password = DataGenerator.get_password(12, True)
            
            return {
                "domain": domain,
                "username": data["login"]["username"],
                "password": password,
                "email": f"{data['login']['username']}@uuf.me",
                "firstName": data["name"]["first"],
                "lastName": data["name"]["last"],
                "addressLine1": f"{data['location']['street']['number']} {data['location']['street']['name']}",
                "addressLine2": f"Apt {random.randint(1, 999)}",
                "city": data["location"]["city"],
                "state": data["location"]["state"],
                "postalCode": str(data["location"]["postcode"]),
                "phone": data["phone"],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            logger.error(f"生成随机用户数据失败: {e}")
            return None

# 数据存储工具类
class DataStorage:
    storage_file = "nc_me_accounts.json"
    
    @staticmethod
    def save_data(data):
        """保存数据到文件"""
        try:
            # 加载现有数据
            all_data = DataStorage.get_all_data()
            
            # 检查用户名是否已存在
            if any(item["username"] == data["username"] for item in all_data):
                logger.warning(f"警告: 用户名 '{data['username']}' 已存在!")
                return False
            
            # 创建要保存的数据，移除filled字段，不生成id字段
            save_data = {k: v for k, v in data.items() if k not in ["filled"]}
            
            # 添加到现有数据
            all_data.append(save_data)
            
            # 保存到文件
            with open(DataStorage.storage_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到 {DataStorage.storage_file}")
            return True
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return False
    
    @staticmethod
    def get_all_data():
        """获取所有保存的数据"""
        try:
            if os.path.exists(DataStorage.storage_file):
                with open(DataStorage.storage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"读取数据失败: {e}")
            return []

# 主程序
def main():
    # 读取域名文件
    domains_data = DomainReader.read_domains_file()
    if not domains_data:
        logger.error("未找到有效的域名数据，程序退出")
        return
    
    # 获取第一个域名和URL
    domain_info = domains_data[0]
    domain = domain_info["domain"]
    confirmation_url = domain_info["url"]
    
    logger.info(f"初始化浏览器...")
    co = ChromiumOptions()
    co.set_browser_path(r'D:\编程相关\project\chrome-win\chrome.exe')
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.69 Safari/537.36')   
    co.set_pref('credentials_enable_service', False)
    co.set_argument('--hide-crash-restore-bubble')
    co.auto_port()
    browser = Chromium(co)
    
    logger.info(f"打开注册页面...")
    reg_tab = browser.new_tab()
    
    # 使用从文件读取的URL而不是硬编码URL
    logger.info(f"访问确认URL: {confirmation_url}")
    reg_tab.get(confirmation_url)
    time.sleep(1)
    reg_tab.get("https://nc.me/account/register")
    
    # 增加页面加载等待时间
    logger.info(f"等待页面加载...")
    
    # 生成随机数据，包含域名信息
    data = DataGenerator.generate_person_data(domain)
    
    # 标记为未填充
    filled = False
    
    # 记录生成的随机用户数据
    logger.info(f"已生成随机用户数据: {data['firstName']} {data['lastName']}")
    logger.info(f"脚本生成的用户名: {data['username']}, 密码: {data['password']}")
    logger.info(f"邮箱: {data['email']}")
    
    # 使用JavaScript填充表单
    try:
        # 填充各个字段
        js_code = f'''
        // 设置表单字段值
        document.querySelector('[name="username"]').value = "{data['username']}";
        document.querySelector('[name="password"]').value = "{data['password']}";
        document.querySelector('[name="password_confirmation"]').value = "{data['password']}";
        document.querySelector('[name="email"]').value = "{data['email']}";
        document.querySelector('[name="first_name"]').value = "{data['firstName']}";
        document.querySelector('[name="last_name"]').value = "{data['lastName']}";
        document.querySelector('[name="address_1"]').value = "{data['addressLine1']}";
        document.querySelector('[name="address_2"]').value = "{data['addressLine2']}";
        document.querySelector('[name="city"]').value = "{data['city']}";
        document.querySelector('[name="state"]').value = "{data['state']}";
        document.querySelector('[name="zipcode"]').value = "{data['postalCode']}";
        
        // 简化电话号码处理
        const phoneField = document.querySelector('[name="phone_intl"]');
        if (phoneField) {{
            phoneField.value = "{data['phone']}";
        }}
        
        // 处理隐藏的电话字段
        const hiddenPhoneField = document.querySelector('[name="phone"]');
        if (hiddenPhoneField) {{
            hiddenPhoneField.value = "{data['phone']}";
        }}
        '''
        
        reg_tab.run_js(js_code)
        logger.info(f"使用JavaScript填充表单成功")
        
        # 添加延迟
        time.sleep(1)
        
        filled = True
    except Exception as e:
        logger.error(f"使用JavaScript填充表单失败: {e}")
        filled = False
    
    input("按Enter继续...")
    
    try:
        # 点击创建账户按钮
        create_account_button = reg_tab.ele('@type=submit')
        create_account_button.click()
        confirm_order_button = reg_tab.ele('@type=submit')
        if confirm_order_button:
            logger.info('创建账户成功')
            confirm_order_button.click()
            logger.info(f"点击确认订单")
    except Exception as e:
        print(f"{Fore.RED}创建账户失败: {e}{Style.RESET_ALL}") 

    # 保存数据
    if filled:
        DataStorage.save_data(data)
    else:
        logger.error(f"保存数据失败")
    
    # 显示保存的信息
    logger.info(f"\n== 账户信息 ==")
    logger.info(f"域名: {domain}")
    logger.info(f"用户名: {data['username']}")
    logger.info(f"密码: {data['password']}")
    logger.info(f"邮箱: {data['email']}")
    logger.info(f"请记录您的登录信息！\n")

  
    
    # 等待用户确认是否继续
    input("按Enter关闭浏览器: ")
    
    browser.quit()
    logger.info(f"程序已完成")

if __name__ == "__main__":
    main()

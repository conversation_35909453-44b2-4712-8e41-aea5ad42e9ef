# AugmentCode Token Helper Enhanced (Fixed) - 修改说明

## 修改概述
基于原版 `augmentcode_token_helper_enhanced.user.js` 进行最小化修改，添加了你要求的两个设置功能，同时保持所有原有功能不变。

## 主要修改内容

### 1. 添加的新功能

#### 🔧 油猴菜单设置
- **新增菜单项**: "⚙️ Cloudflare Email Routing设置"
- **功能**: 弹出设置界面，配置自定义域名后缀和@uuf.me接收邮箱
- **保持原有**: "🔗 生成授权链接" 和 "👁️ 显示/隐藏 Token Helper" 菜单

#### 📧 邮箱生成逻辑升级
- **原逻辑**: 从4个预设域名中选择生成邮箱
- **新逻辑**: 
  - 如果配置了自定义域名 → 生成 `随机前缀@自定义域名`
  - 如果未配置 → 使用原有的域名选择逻辑
- **界面变化**: 当配置自定义域名后，域名选择框会被禁用并显示配置状态

#### 🔐 验证码自动获取
- **新增功能**: "获取验证码" 按钮
- **工作原理**: 
  - 通过MailCX API连接到配置的@uuf.me邮箱
  - 自动轮询获取最新邮件
  - 使用正则表达式提取6位数字验证码
  - 自动删除已处理的邮件
- **状态管理**: 详细的进度显示和错误处理

### 2. 保持不变的功能

#### ✅ 完全保留的原有功能
- **OAuth授权链接生成**: 完整保留原有逻辑
- **Token自动检测**: URL检测、页面内容检测、剪贴板检测
- **Token获取流程**: PKCE认证、状态验证、Token获取
- **UI界面**: 拖拽、标签页切换、样式设计
- **输入框查找**: 邮箱输入框和验证码输入框的智能识别
- **数据存储**: 邮箱和验证码的本地保存

#### 🔄 轻微调整的功能
- **使用说明**: 根据是否配置自定义域名显示不同的使用流程
- **状态显示**: 增加了Cloudflare相关的状态信息
- **按钮状态**: 验证码获取按钮在未配置接收邮箱时会被禁用

### 3. 技术实现细节

#### 📦 新增的核心组件
```javascript
// Cloudflare配置管理
const CF_CONFIG = {
    customDomain: GM_getValue('cf-custom-domain', ''),
    receiverEmail: GM_getValue('cf-receiver-email', ''),
    // ...其他配置
};

// MailCX API客户端
class MailCX {
    // 完整的API客户端实现
}

// 验证码提取函数
function extractVerificationCode(emailContent) {
    // 支持多种验证码格式
}
```

#### 🔗 集成方式
- **无侵入性**: 新功能通过配置开关控制，不影响原有流程
- **向后兼容**: 未配置时完全使用原有逻辑
- **渐进增强**: 配置后自动启用新功能

### 4. 使用流程对比

#### 原版本流程
1. 选择域名 → 生成邮箱 → 填充到页面
2. 手动查看邮件 → 手动输入验证码 → 填充验证码

#### 新版本流程（配置后）
1. 生成自定义域名邮箱 → 填充到页面
2. 点击"获取验证码" → 自动获取 → 填充验证码

#### 新版本流程（未配置）
- 完全等同于原版本流程

### 5. 配置说明

#### 设置界面内容
- **自定义域名后缀**: 例如 `example.com`
- **接收邮箱**: 例如 `<EMAIL>`
- **配置说明**: 详细的Cloudflare Email Routing配置指导
- **测试连接**: 验证@uuf.me邮箱API是否正常

#### 前置条件
1. 在Cloudflare中配置Email Routing
2. 设置catch-all转发规则: `*@yourdomain.com` → `<EMAIL>`
3. 在脚本中配置相应的域名和接收邮箱

## 文件信息

- **新文件名**: `augmentcode_token_helper_enhanced_fixed.user.js`
- **版本号**: 2.4
- **兼容性**: 完全兼容原版本的所有功能
- **依赖**: 需要有效的@uuf.me邮箱用于API访问

## 总结

这个修改版本严格按照你的要求，只添加了两个设置功能，没有移除任何原有功能。所有的OAuth授权链接生成、Token获取、菜单命令等功能都完整保留。新功能通过配置开关控制，确保在未配置时不会影响原有的使用体验。

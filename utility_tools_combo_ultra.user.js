// ==UserScript==
// @name         QuickTools
// @namespace    http://tampermonkey.net/
// @version      0.3
// @description  QuickTools：生成随机字符串、时间戳转换、Base64编解码、临时邮箱
// <AUTHOR> Agent
// @match        *://*/*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @grant        GM_xmlhttpRequest
// @connect      api.mail.cx
// @connect      randomuser.me
// ==/UserScript==

(function() {
    'use strict';

    // ==================== 配置 ====================

    // 默认配置
    const DEFAULT_CONFIG = {
        showOnAllSites: true,         // 是否在所有网站显示
        siteWhitelist: [],            // 网站白名单
        buttonPosition: {             // 按钮位置
            top: null,
            left: null,
            bottom: '20px',
            right: '20px'
        },
        defaultEmail: '',              // 默认邮箱
        useCustomEmail: false         // 是否使用默认邮箱
    };

    // 获取用户配置
    function getConfig() {
        const savedConfig = GM_getValue('utilityTools_config', null);
        return savedConfig ? JSON.parse(savedConfig) : DEFAULT_CONFIG;
    }

    // 保存用户配置
    function saveConfig(config) {
        GM_setValue('utilityTools_config', JSON.stringify(config));
    }

    // 当前配置
    let CONFIG = getConfig();

    // 检查当前网站是否应该显示工具
    function shouldShowOnCurrentSite() {
        if (CONFIG.showOnAllSites) return true;

        const currentHost = window.location.hostname;
        return CONFIG.siteWhitelist.some(site => {
            // 处理可能的空行和空格
            site = site.trim();
            if (!site) return false;

            // 移除可能的协议前缀
            site = site.replace(/^https?:\/\//, '');

            // 移除可能的路径和参数
            site = site.split('/')[0];

            // 直接比较域名
            return currentHost === site ||
                   // 支持子域名
                   currentHost.endsWith('.' + site);
        });
    }

    // ==================== 邮箱相关功能 ====================

    // MailCX错误类
    class MailCXError extends Error {
        constructor(message) {
            super(message);
            this.name = 'MailCXError';
        }
    }

    // MailCX API客户端
    class MailCX {
        constructor() {
            this.BASE_URL = 'https://api.mail.cx/api/v1';
            this.token = null;
            this.tokenExpireTime = null;
            // token有效期设置为5分钟
            this.TOKEN_VALIDITY_MS = 5 * 60 * 1000;
        }

        // 检查token是否过期
        isTokenExpired() {
            // 如果没有token或过期时间，则认为已过期
            if (!this.token || !this.tokenExpireTime) {
                return true;
            }
            // 检查当前时间是否超过过期时间
            return Date.now() > this.tokenExpireTime;
        }

        async makeRequest(method, endpoint, options = {}, retryCount = 0) {
            const url = `${this.BASE_URL}${endpoint}`;
            const headers = {
                'accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ...(this.token ? {'Authorization': `Bearer ${this.token}`} : {}),
                ...options.headers
            };

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: method,
                    url: url,
                    headers: headers,
                    data: options.body,
                    timeout: 30000,
                    onload: async (response) => {
                        if (response.status >= 200 && response.status < 300) {
                            try {
                                const result = response.responseText ? JSON.parse(response.responseText) : {};
                                resolve(result);
                            } catch (e) {
                                resolve(response.responseText);
                            }
                        } else if ((response.status === 401 || response.status === 403) && retryCount < 2) {
                            // 如果是认证错误且重试次数小于2，则尝试刷新token并重试
                            console.log(`Token可能已过期，状态码: ${response.status}，尝试刷新token并重试请求...`);
                            try {
                                // 强制重新获取token
                                this.token = null;
                                this.tokenExpireTime = null;
                                await this.authorize();

                                // 重试请求
                                const result = await this.makeRequest(method, endpoint, options, retryCount + 1);
                                resolve(result);
                            } catch (error) {
                                reject(new MailCXError(`刷新token失败: ${error.message}`));
                            }
                        } else {
                            reject(new MailCXError(`请求失败: ${response.status} ${response.statusText}`));
                        }
                    },
                    onerror: function(error) {
                        reject(new MailCXError(`请求错误: ${error}`));
                    }
                });
            });
        }

        async authorize() {
            try {
                const response = await this.makeRequest('POST', '/auth/authorize_token', {
                    headers: {'Authorization': 'Bearer undefined'}
                }, 0); // 传入重试次数0，避免无限循环

                if (typeof response === 'string') {
                    this.token = response.replace(/"/g, '');
                    // 设置token过期时间（当前时间 + 有效期）
                    this.tokenExpireTime = Date.now() + this.TOKEN_VALIDITY_MS;
                    console.log('Token已更新，有效期至:', new Date(this.tokenExpireTime).toLocaleString());
                } else {
                    throw new MailCXError('无法获取token');
                }
            } catch (e) {
                console.error('认证失败:', e);
                throw e;
            }
        }

        async getMailbox(email) {
            // 检查token是否过期
            if (!this.token || this.isTokenExpired()) {
                console.log('Token不存在或已过期，重新获取...');
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}`);
        }

        async getMessage(email, messageId) {
            // 检查token是否过期
            if (!this.token || this.isTokenExpired()) {
                console.log('Token不存在或已过期，重新获取...');
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMessage(email, messageId) {
            // 检查token是否过期
            if (!this.token || this.isTokenExpired()) {
                console.log('Token不存在或已过期，重新获取...');
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMailbox(email) {
            // 检查token是否过期
            if (!this.token || this.isTokenExpired()) {
                console.log('Token不存在或已过期，重新获取...');
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}`);
        }
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return dateString; // 如果解析失败，返回原始字符串
            }

            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        } catch (e) {
            return dateString;
        }
    }

    // ==================== 工具函数 ====================

    // 生成指定长度的随机字符串
    function generateRandomString(length = 10, includeSymbols = false) {
        let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        if (includeSymbols) {
            chars += '!@#$%^&*_-+=';
        }
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 生成UUID
    function generateUUID() {
        // 标准的UUID v4格式
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // 将Unix时间戳转换为格式化日期 (YYYY-MM-DD)
    function timestampToDate(timestamp) {
        // 检查时间戳是秒数(10位)还是毫秒数(13位)
        if (timestamp.length === 10) {
            timestamp = timestamp + '000'; // 转换为毫秒
        }

        const date = new Date(parseInt(timestamp));

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            return '无效的时间戳';
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    }

    // Base64编码
    function base64Encode(str) {
        try {
            return btoa(str);
        } catch (e) {
            // 处理UTF-8字符
            const binaryString = new TextEncoder().encode(str);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString[i];
            }
            let binary = '';
            bytes.forEach(byte => binary += String.fromCharCode(byte));
            return btoa(binary);
        }
    }

    // Base64解码
    function base64Decode(str) {
        try {
            const binary = atob(str);
            const bytes = new Uint8Array(binary.length);
            for (let i = 0; i < binary.length; i++) {
                bytes[i] = binary.charCodeAt(i);
            }
            return new TextDecoder().decode(bytes);
        } catch (e) {
            return '无效的Base64字符串';
        }
    }

    // ==================== UI创建 ====================

    // 创建主要UI容器
    function createUI() {
        // 创建主容器
        const container = document.createElement('div');
        container.id = 'utility-tools-container';
        container.className = 'utility-tools-container';

        // 创建切换按钮
        const toggleButton = document.createElement('button');
        toggleButton.id = 'utility-tools-toggle';
        toggleButton.className = 'utility-tools-toggle';
        toggleButton.textContent = '🛠️';
        toggleButton.title = 'QuickTools';

        // 应用保存的按钮位置
        if (CONFIG.buttonPosition) {
            // 检查是否有保存的top和left值（表示按钮被拖动过）
            if (CONFIG.buttonPosition.top && CONFIG.buttonPosition.left) {
                toggleButton.style.top = CONFIG.buttonPosition.top;
                toggleButton.style.left = CONFIG.buttonPosition.left;
                // 重置bottom和right，确保位置由top和left控制
                toggleButton.style.bottom = 'auto';
                toggleButton.style.right = 'auto';
            } else {
                // 使用默认位置或保存的bottom/right值
                if (CONFIG.buttonPosition.bottom) toggleButton.style.bottom = CONFIG.buttonPosition.bottom;
                if (CONFIG.buttonPosition.right) toggleButton.style.right = CONFIG.buttonPosition.right;
            }
        }

        // 创建面板
        const panel = document.createElement('div');
        panel.id = 'utility-tools-panel';
        panel.className = 'utility-tools-panel';

        // 创建面板标题栏（用于拖动）
        const header = document.createElement('div');
        header.className = 'utility-tools-header';
        header.innerHTML = `
            <span>QuickTools</span>
            <div class="utility-tools-header-buttons">
                <button id="utility-tools-settings" class="utility-tools-header-btn" style="font-size: 16px; width: 28px; height: 28px; text-align: center; padding: 0;">⚙️</button>
                <button id="utility-tools-close" class="utility-tools-header-btn" style="font-size: 20px; width: 28px; height: 28px; text-align: center; padding: 0;">×</button>
            </div>
        `;

        // 创建标签页导航
        const tabNav = document.createElement('div');
        tabNav.className = 'utility-tools-tabs';
        tabNav.innerHTML = `
            <button id="tab-random" class="utility-tools-tab-btn active">随机字符</button>
            <button id="tab-timestamp" class="utility-tools-tab-btn">时间转换</button>
            <button id="tab-base64" class="utility-tools-tab-btn">Base64码</button>
            <button id="tab-identity" class="utility-tools-tab-btn">身份生成</button>
            <button id="tab-email" class="utility-tools-tab-btn">临时邮箱</button>
        `;

        // 创建内容容器
        const content = document.createElement('div');
        content.className = 'utility-tools-content';

        // 添加随机字符串生成器
        const randomStringSection = document.createElement('div');
        randomStringSection.className = 'utility-tools-section utility-tools-tab-content active';
        randomStringSection.id = 'content-random';
        randomStringSection.innerHTML = `
            <div class="utility-tools-input-group">
                <div class="utility-tools-options">
                    <label>长度: <input type="number" id="random-string-length" value="10" min="1" max="100"></label>
                    <label><input type="checkbox" id="include-symbols"> 包含符号 (!@#$%^&*_-+=)</label>
                </div>
                <div class="utility-tools-button-group">
                    <button id="generate-random-string">生成随机字符串</button>
                    <button id="generate-uuid">生成UUID</button>
                </div>
            </div>
            <div class="utility-tools-result">
                <input type="text" id="random-string-result" readonly>
                <button id="copy-random-string" class="utility-tools-copy-btn">复制</button>
            </div>
        `;

        // 添加时间戳转换器
        const timestampSection = document.createElement('div');
        timestampSection.className = 'utility-tools-section utility-tools-tab-content';
        timestampSection.id = 'content-timestamp';
        timestampSection.style.display = 'none';
        timestampSection.innerHTML = `
            <div class="utility-tools-input-group">
                <input type="text" id="timestamp-input" placeholder="输入Unix时间戳 (例如: 1767588074)">
                <button id="convert-timestamp">转换</button>
            </div>
            <div class="utility-tools-result">
                <input type="text" id="timestamp-result" readonly>
                <button id="copy-timestamp" class="utility-tools-copy-btn">复制</button>
            </div>
        `;

        // 添加base64编解码器
        const base64Section = document.createElement('div');
        base64Section.className = 'utility-tools-section utility-tools-tab-content';
        base64Section.id = 'content-base64';
        base64Section.style.display = 'none';
        base64Section.innerHTML = `
            <div class="utility-tools-input-group">
                <textarea id="base64-input" placeholder="输入要编码或解码的文本"></textarea>
                <div class="utility-tools-button-group">
                    <button id="encode-base64">编码</button>
                    <button id="decode-base64">解码</button>
                </div>
            </div>
            <div class="utility-tools-result">
                <textarea id="base64-result" readonly></textarea>
                <button id="copy-base64" class="utility-tools-copy-btn">复制</button>
            </div>
        `;

        // 添加身份生成功能
        const identitySection = document.createElement('div');
        identitySection.className = 'utility-tools-section utility-tools-tab-content';
        identitySection.id = 'content-identity';
        identitySection.style.display = 'none';
        identitySection.innerHTML = `
            <div class="utility-tools-identity-container" style="margin: 0; padding: 0;">
                <div style="margin: 0 0 0 0; padding: 0; display: flex; justify-content: center; align-items: center;">
                    <div class="utility-tools-button-group" style="display: flex; gap: 6px; margin: 0; width: 80%;">
                        <button id="generate-identity" class="utility-tools-input-group button utility-tools-primary-btn" style="flex: 0 0 50%; padding: 4px 0; font-size: 12px; height: 24px; line-height: 24px; background-color: #3f51b5; color: white; border: none; border-radius: 4px; cursor: pointer; transition: all 0.2s ease; font-weight: 500; text-align: center; display: flex; align-items: center; justify-content: center; white-space: nowrap;">生成身份信息</button>
                        <button id="copy-all-identity" class="utility-tools-input-group button" style="flex: 0 0 50%; padding: 4px 0; font-size: 12px; height: 24px; line-height: 24px; background-color: #757de8; color: white; border: none; border-radius: 4px; cursor: pointer; transition: all 0.2s ease; font-weight: 500; text-align: center; display: flex; align-items: center; justify-content: center; white-space: nowrap;">复制全部信息</button>
                    </div>
                </div>
                <div id="identity-result" class="utility-tools-identity-result" style="margin: 0; padding: 0;">
                    <div class="utility-tools-identity-empty" style="padding: 10px 0; font-size: 12px; text-align: center;">点击生成按钮获取身份信息</div>
                </div>
            </div>
        `;

        // 添加临时邮箱功能
        const emailSection = document.createElement('div');
        emailSection.className = 'utility-tools-section utility-tools-tab-content';
        emailSection.id = 'content-email';
        emailSection.style.display = 'none';
        emailSection.innerHTML = `
            <div class="utility-tools-email-container">
                <!-- 工具栏区域 - 移动到顶部 -->
                <div class="utility-tools-email-toolbar" style="margin-bottom: 6px;">
                    <button id="refresh-emails" class="utility-tools-button utility-tools-button-icon" title="刷新邮件" style="width: 28px; height: 28px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="utility-tools-icon">
                            <polyline points="1 4 1 10 7 10"></polyline>
                            <polyline points="23 20 23 14 17 14"></polyline>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                        </svg>
                    </button>
                    <button id="clear-mailbox" class="utility-tools-button utility-tools-button-icon utility-tools-button-danger" title="清空邮箱" style="width: 28px; height: 28px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="utility-tools-icon">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                    </button>
                </div>

                <!-- 邮箱输入框区域 -->
                <div class="utility-tools-email-input-container" style="display: flex; align-items: center; gap: 6px;">
                    <input type="text" id="email-input" class="utility-tools-input" placeholder="输入邮箱地址" style="flex: 1; padding: 6px 8px; height: 28px;">
                    <button id="copy-email" class="utility-tools-copy-btn" title="复制邮箱地址" style="min-width: 50px; font-size: 12px; padding: 3px 6px; height: 28px;">复制</button>
                </div>

                <!-- 邮箱类型提示区域 -->
                <div class="utility-tools-email-type-container" style="margin: 2px 0;">
                    <div id="email-type-hint" class="utility-tools-email-hint" style="font-size: 12px;">当前使用: 未设置</div>
                </div>

                <!-- 按钮区域 -->
                <div class="utility-tools-email-buttons-container" style="display: flex; gap: 6px;">
                    <button id="default-email-btn" class="utility-tools-button utility-tools-button-primary" style="font-size: 12px; padding: 4px 8px; height: 28px;">默认邮箱</button>
                    <div class="utility-tools-email-domain-group" style="display: flex; gap: 4px;">
                        <button id="random-email-btn" class="utility-tools-button utility-tools-button-secondary" style="font-size: 12px; padding: 4px 8px; height: 28px;">随机</button>
                        <select id="domain-select" class="utility-tools-domain-select" style="height: 28px; font-size: 12px; padding: 0 4px;">
                            <option value="qabq.com">@qabq.com</option>
                            <option value="nqmo.com">@nqmo.com</option>
                            <option value="end.tw">@end.tw</option>
                            <option value="uuf.me" selected>@uuf.me</option>
                            <option value="yzm.de">@yzm.de</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 邮件列表区域 -->
            <div id="emails-list" class="utility-tools-emails-list" style="max-height: 260px; overflow-y: auto;">
                <div class="utility-tools-email-empty" style="font-size: 12px; padding: 10px;">点击刷新按钮查看邮件</div>
            </div>
        `;

        // 创建设置面板
        const settingsPanel = document.createElement('div');
        settingsPanel.id = 'utility-tools-settings-panel';
        settingsPanel.className = 'utility-tools-settings-panel';
        settingsPanel.style.display = 'none';
        settingsPanel.innerHTML = `
            <div class="utility-tools-settings-header">
                <h3>设置</h3>
                <button id="settings-close" class="utility-tools-settings-close">×</button>
            </div>
            <div class="utility-tools-settings-group">
                <label>
                    <input type="checkbox" id="show-on-all-sites" ${CONFIG.showOnAllSites ? 'checked' : ''}>
                    在所有网站显示
                </label>
            </div>
            <div class="utility-tools-settings-group" id="whitelist-container">
                <label>网站白名单 （当不在所有网站显示时生效）：</label>
                <p style="font-size: 13px; color: #333; margin-bottom: 5px; font-weight: 500;">每行一个网站域名，无需输入 http:// 或 https://</p>
                <textarea id="site-whitelist" style="font-size: 15px; font-family: 'Segoe UI', Arial, sans-serif; color: #000; line-height: 1.6; font-weight: 500;" placeholder="例如：\naugmentcode.com\nnamecheap.com">${CONFIG.siteWhitelist.join('\n')}</textarea>
            </div>
            <div class="utility-tools-settings-group">
                <label>
                    <input type="checkbox" id="use-custom-email" ${CONFIG.useCustomEmail ? 'checked' : ''}>
                    使用默认邮箱
                </label>
                <p style="font-size: 12px; color: #666; margin-bottom: 5px;">启用后将自动填充设置的默认邮箱地址</p>
                <input type="text" id="default-email" class="utility-tools-input utility-tools-text-input" style="margin-top: 5px; display: block; width: 100%;" value="${CONFIG.defaultEmail}" placeholder="输入默认邮箱地址">
            </div>
            <div class="utility-tools-settings-group" style="text-align: right;">
                <button id="save-settings" class="utility-tools-save-btn utility-tools-primary-btn">保存设置</button>
            </div>
        `;

        // 组装UI
        content.appendChild(randomStringSection);
        content.appendChild(timestampSection);
        content.appendChild(base64Section);
        content.appendChild(identitySection);
        content.appendChild(emailSection);

        panel.appendChild(header);
        panel.appendChild(tabNav);
        panel.appendChild(content);
        panel.appendChild(settingsPanel);

        container.appendChild(toggleButton);
        container.appendChild(panel);

        document.body.appendChild(container);

        // 初始化时隐藏面板
        panel.style.display = 'none';

        // 确保初始时内容状态正确
        setTimeout(() => {
            // 重置所有标签内容为隐藏
            document.querySelectorAll('.utility-tools-tab-content').forEach(content => {
                content.style.display = 'none';
                content.classList.remove('active');
            });

            // 显示随机字符串标签内容
            const randomContent = document.getElementById('content-random');
            if (randomContent) {
                randomContent.style.display = 'block';
                randomContent.classList.add('active');
            }

            // 激活随机字符串标签
            const randomTab = document.getElementById('tab-random');
            if (randomTab) {
                randomTab.classList.add('active');
            }
        }, 100);

        return {
            container,
            toggleButton,
            panel,
            header,
            settingsPanel,
            tabNav
        };
    }

    // ==================== 事件处理 ====================

    function setupEventHandlers(ui) {
        // 切换面板可见性
        ui.toggleButton.addEventListener('click', (e) => {
            // 检查是否是拖动后的点击
            if (ui.toggleButton.hasAttribute('data-was-dragged') && ui.toggleButton.getAttribute('data-was-dragged') === 'true') {
                // 如果是拖动后的点击，移除标记并忽略此次点击
                ui.toggleButton.removeAttribute('data-was-dragged');
                return;
            }

            // 如果面板当前是隐藏的，则显示并计算位置
            if (ui.panel.style.display === 'none' || !ui.panel.style.display) {
                // 计算面板位置，使其显示在按钮附近且不超出屏幕
                positionPanelNearButton(ui.toggleButton, ui.panel);
                ui.panel.style.display = 'block';
            } else {
                ui.panel.style.display = 'none';
            }

            // 如果设置面板打开，则关闭它
            ui.settingsPanel.style.display = 'none';
        });

        // 关闭按钮
        document.getElementById('utility-tools-close').addEventListener('click', () => {
            ui.panel.style.display = 'none';
        });

        // 设置按钮
        document.getElementById('utility-tools-settings').addEventListener('click', () => {
            // 直接切换设置面板的显示状态，不使用遮罩层
            if (ui.settingsPanel.style.display === 'none' || !ui.settingsPanel.style.display) {
                ui.settingsPanel.style.display = 'block';
            } else {
                ui.settingsPanel.style.display = 'none';
            }
        });

        // 标签页切换
        document.getElementById('tab-random').addEventListener('click', () => switchTab('random'));
        document.getElementById('tab-timestamp').addEventListener('click', () => switchTab('timestamp'));
        document.getElementById('tab-base64').addEventListener('click', () => switchTab('base64'));
        document.getElementById('tab-identity').addEventListener('click', () => switchTab('identity'));
        document.getElementById('tab-email').addEventListener('click', () => switchTab('email'));

        // 标签页切换函数
        function switchTab(tabId) {
            console.log(`切换到标签页: ${tabId}`);

            try {
            // 隐藏所有内容
                const allContents = document.querySelectorAll('.utility-tools-tab-content');
                console.log(`找到内容元素数量: ${allContents.length}`);
                allContents.forEach(content => {
                content.style.display = 'none';
                    content.classList.remove('active');
            });

            // 移除所有标签的活动状态
                const allTabs = document.querySelectorAll('.utility-tools-tab-btn');
                console.log(`找到标签按钮数量: ${allTabs.length}`);
                allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的内容并激活对应标签
                const contentElement = document.getElementById(`content-${tabId}`);
                if (contentElement) {
                    contentElement.style.display = 'block';
                    contentElement.classList.add('active');
                    console.log(`显示标签内容: content-${tabId}`);
                } else {
                    console.error(`找不到内容元素: content-${tabId}`);
                }

                const tabElement = document.getElementById(`tab-${tabId}`);
                if (tabElement) {
                    tabElement.classList.add('active');
                    console.log(`激活标签: tab-${tabId}`);
                } else {
                    console.error(`找不到标签元素: tab-${tabId}`);
                }

                // 备用方法：直接通过CSS选择器强制显示
                document.querySelectorAll(`#content-${tabId}`).forEach(el => {
                    el.style.display = 'block';
                    el.classList.add('active');
                });

                // 根据标签页类型，懒加载初始化对应功能
                switch(tabId) {
                    case 'email':
                        // 如果是邮箱标签页，初始化邮箱功能
                        if (!window.mailClientInitialized) {
                            initializeEmailClient();
                            window.mailClientInitialized = true;
                            console.log('已初始化邮箱客户端');
                        }
                        break;
                    case 'identity':
                        // 如果是身份生成标签页，初始化身份生成功能
                        if (!window.identityGeneratorInitialized) {
                            initializeIdentityGenerator();
                            window.identityGeneratorInitialized = true;
                            console.log('已初始化身份生成功能');
                        }
                        break;
                    case 'timestamp':
                        // 如果是时间戳标签页，初始化时间戳功能
                        if (!window.timestampInitialized) {
                            initializeTimestampConverter();
                            window.timestampInitialized = true;
                            console.log('已初始化时间戳转换功能');
                        }
                        break;
                    case 'base64':
                        // 如果是Base64标签页，初始化Base64功能
                        if (!window.base64Initialized) {
                            initializeBase64Converter();
                            window.base64Initialized = true;
                            console.log('已初始化Base64编解码功能');
                        }
                        break;
                    case 'random':
                        // 如果是随机字符串标签页，初始化随机字符串功能
                        if (!window.randomStringInitialized) {
                            initializeRandomStringGenerator();
                            window.randomStringInitialized = true;
                            console.log('已初始化随机字符串生成功能');
                        }
                        break;
                }
            } catch (error) {
                console.error('标签切换出错:', error);
            }
        }

        // 保存设置按钮
        document.getElementById('save-settings').addEventListener('click', () => {
            // 获取设置值
            const showOnAllSites = document.getElementById('show-on-all-sites').checked;
            const whitelistText = document.getElementById('site-whitelist').value;
            const whitelist = whitelistText.split('\n')
                .map(site => site.trim())
                .filter(site => site.length > 0);

            // 获取邮箱设置
            const useCustomEmail = document.getElementById('use-custom-email').checked;
            const defaultEmail = document.getElementById('default-email').value.trim();

            // 更新配置
            CONFIG.showOnAllSites = showOnAllSites;
            CONFIG.siteWhitelist = whitelist;
            CONFIG.useCustomEmail = useCustomEmail;
            CONFIG.defaultEmail = defaultEmail;

            // 保存按钮位置
            CONFIG.buttonPosition = {
                top: ui.toggleButton.style.top || null,
                left: ui.toggleButton.style.left || null,
                bottom: ui.toggleButton.style.bottom || null,
                right: ui.toggleButton.style.right || null
            };

            // 保存配置
            saveConfig(CONFIG);

            // 显示成功消息
            const saveBtn = document.getElementById('save-settings');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '已保存';
            setTimeout(() => {
                saveBtn.textContent = originalText;
                ui.settingsPanel.style.display = 'none';
            }, 1000);

            // 更新白名单提示文字颜色
            const whitelistHint = document.querySelector('#whitelist-container p');
            if (whitelistHint) {
                whitelistHint.style.color = showOnAllSites ? '#999' : '#666';
                whitelistHint.style.fontWeight = showOnAllSites ? 'normal' : 'bold';
            }
        });

        // 设置面板关闭按钮
        document.getElementById('settings-close').addEventListener('click', () => {
            ui.settingsPanel.style.display = 'none';
        });

        // 白名单提示文字更新
        document.getElementById('show-on-all-sites').addEventListener('change', (e) => {
            const whitelistHint = document.querySelector('#whitelist-container p');
            if (whitelistHint) {
                whitelistHint.style.color = e.target.checked ? '#999' : '#666';
                whitelistHint.style.fontWeight = e.target.checked ? 'normal' : 'bold';
            }
        });

        // 使面板可拖动
        makeDraggable(ui.panel, ui.header);

        // 使切换按钮可拖动
        makeDraggable(ui.toggleButton, ui.toggleButton);

        // 初始化随机字符串生成器功能
        function initializeRandomStringGenerator() {
            console.log('初始化随机字符串生成器功能');

            // 随机字符串生成器
            document.getElementById('generate-random-string').addEventListener('click', () => {
                const length = parseInt(document.getElementById('random-string-length').value) || 10;
                const includeSymbols = document.getElementById('include-symbols').checked;
                document.getElementById('random-string-result').value = generateRandomString(length, includeSymbols);
            });

            // UUID生成器
            document.getElementById('generate-uuid').addEventListener('click', () => {
                document.getElementById('random-string-result').value = generateUUID();
            });

            // 复制按钮
            document.getElementById('copy-random-string').addEventListener('click', () => {
                copyToClipboard('random-string-result');
            });

            // 初始生成一个随机字符串
            document.getElementById('random-string-result').value = generateRandomString(10, false);
        }

        // 初始化时间戳转换器功能
        function initializeTimestampConverter() {
            console.log('初始化时间戳转换器功能');

            // 时间戳转换器
            document.getElementById('convert-timestamp').addEventListener('click', () => {
                const timestamp = document.getElementById('timestamp-input').value.trim();
                document.getElementById('timestamp-result').value = timestampToDate(timestamp);
            });

            // 复制按钮
            document.getElementById('copy-timestamp').addEventListener('click', () => {
                copyToClipboard('timestamp-result');
            });

            // 可以添加当前时间戳作为示例
            const now = Math.floor(Date.now() / 1000);
            document.getElementById('timestamp-input').placeholder = `输入Unix时间戳 (例如: ${now})`;
        }

        // 初始化Base64编解码器功能
        function initializeBase64Converter() {
            console.log('初始化Base64编解码器功能');

            // Base64编码器
            document.getElementById('encode-base64').addEventListener('click', () => {
                const input = document.getElementById('base64-input').value;
                document.getElementById('base64-result').value = base64Encode(input);
            });

            // Base64解码器
            document.getElementById('decode-base64').addEventListener('click', () => {
                const input = document.getElementById('base64-input').value;
                document.getElementById('base64-result').value = base64Decode(input);
            });

            // 复制按钮
            document.getElementById('copy-base64').addEventListener('click', () => {
                copyToClipboard('base64-result');
            });
        }

        // 初始化邮箱功能
        function initializeEmailClient() {
            // 创建MailCX实例
            const mailClient = new MailCX();

            // 获取邮箱相关元素
            const emailInput = document.getElementById('email-input');
            const emailTypeHint = document.getElementById('email-type-hint');
            const defaultEmailBtn = document.getElementById('default-email-btn');
            const randomEmailBtn = document.getElementById('random-email-btn');
            const domainSelect = document.getElementById('domain-select');
            const refreshEmailsBtn = document.getElementById('refresh-emails');
            const clearMailboxBtn = document.getElementById('clear-mailbox');
            const emailsList = document.getElementById('emails-list');

            // 更新邮箱类型提示
            function updateEmailTypeHint() {
                if (CONFIG.useCustomEmail && CONFIG.defaultEmail && emailInput.value === CONFIG.defaultEmail) {
                    emailTypeHint.textContent = "当前使用: 默认邮箱";
                    emailTypeHint.style.color = "#4CAF50";
                } else if (emailInput.value.includes('@mail.cx') ||
                           emailInput.value.includes('@qabq.com') ||
                           emailInput.value.includes('@nqmo.com') ||
                           emailInput.value.includes('@end.tw') ||
                           emailInput.value.includes('@uuf.me') ||
                           emailInput.value.includes('@yzm.de')) {
                    emailTypeHint.textContent = "当前使用: 随机邮箱";
                    emailTypeHint.style.color = "#2196F3";
                } else if (emailInput.value) {
                    emailTypeHint.textContent = "当前使用: 自定义邮箱";
                    emailTypeHint.style.color = "#FFA000";
                } else {
                    emailTypeHint.textContent = "当前使用: 未设置";
                    emailTypeHint.style.color = "#999";
                }

                // 根据是否有默认邮箱来禁用或启用默认邮箱按钮
                if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
                    defaultEmailBtn.disabled = false;
                    defaultEmailBtn.style.opacity = "1";
                } else {
                    defaultEmailBtn.disabled = true;
                    defaultEmailBtn.style.opacity = "0.5";
                }
            }

            // 如果设置了默认邮箱，自动填充
            if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
                emailInput.value = CONFIG.defaultEmail;
            }

            // 始终调用更新提示函数，确保"当前使用"始终显示
            updateEmailTypeHint();

            // 监听输入框变化，更新提示
            emailInput.addEventListener('input', updateEmailTypeHint);

            // 使用默认邮箱按钮事件
            defaultEmailBtn.addEventListener('click', () => {
                if (CONFIG.useCustomEmail && CONFIG.defaultEmail) {
                    emailInput.value = CONFIG.defaultEmail;
                    updateEmailTypeHint();
                } else {
                    alert('请先在设置中配置默认邮箱');
                }
            });

            // 生成随机邮箱
            function generateRandomEmail() {
                const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
                const selectedDomain = domainSelect.value;

                let randomString = '';
                for (let i = 0; i < 8; i++) {
                    randomString += characters.charAt(Math.floor(Math.random() * characters.length));
                }

                return randomString + '@' + selectedDomain;
            }

            // 绑定随机邮箱按钮事件
            randomEmailBtn.addEventListener('click', () => {
                emailInput.value = generateRandomEmail();
                updateEmailTypeHint();

                // 顺便同步一下域名菜单中的选项
                const atIndex = emailInput.value.indexOf('@');
                if (atIndex > 0) {
                    const domain = emailInput.value.substring(atIndex + 1);
                    for (let i = 0; i < domainSelect.options.length; i++) {
                        if (domainSelect.options[i].value === domain) {
                            domainSelect.selectedIndex = i;
                            break;
                        }
                    }
                }
            });

            // 当选择域名时，如果邮箱是随机生成的，则更新域名
            domainSelect.addEventListener('change', () => {
                const emailValue = emailInput.value;
                const atIndex = emailValue.indexOf('@');

                if (atIndex > 0 && emailTypeHint.textContent.includes('随机邮箱')) {
                    // 只保留@前面的部分，添加新域名
                    emailInput.value = emailValue.substring(0, atIndex) + '@' + domainSelect.value;
                    updateEmailTypeHint();
                }
            });

            // 刷新邮件列表
            async function refreshMessages() {
                const email = emailInput.value.trim();
                if (!email) {
                    alert('请输入邮箱地址');
                    return;
                }

                // 添加刷新动画
                refreshEmailsBtn.classList.add('refreshing');

                // 显示加载状态
                emailsList.innerHTML = '<div class="utility-tools-email-loading" style="padding: 10px; text-align: center; color: #757575; font-size: 13px; background-color: #f5f5f5; border-radius: 8px; margin: 10px auto;">正在加载...</div>';

                try {
                    const messages = await mailClient.getMailbox(email);
                    emailsList.innerHTML = '';

                    if (Array.isArray(messages) && messages.length > 0) {
                        for (const msg of messages) {
                            const messageDiv = document.createElement('div');
                            messageDiv.className = 'utility-tools-email-message';
                            messageDiv.style.cssText = 'padding: 8px; border-bottom: 1px solid #e0e0e0; background-color: #fff; margin-bottom: 2px; border-radius: 6px; font-size: 12px;';

                            const headerDiv = document.createElement('div');
                            headerDiv.className = 'utility-tools-email-message-header';
                            headerDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; gap: 6px;';

                            const subjectDiv = document.createElement('div');
                            subjectDiv.className = 'utility-tools-email-message-subject';
                            subjectDiv.style.cssText = 'flex-grow: 2; cursor: pointer; padding: 4px 6px; border-radius: 4px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 60%; font-weight: 500; color: #212121; font-size: 12px;';
                            subjectDiv.textContent = msg.subject || '(无主题)';

                            // 时间显示在同一行
                            const timeDiv = document.createElement('div');
                            timeDiv.className = 'utility-tools-email-message-time';
                            timeDiv.style.cssText = 'font-size: 11px; color: #757575; flex-grow: 1; text-align: right; white-space: nowrap; padding-right: 6px; font-weight: 500;';
                            timeDiv.textContent = msg.date ? formatDateTime(msg.date) : '未知时间';

                            const actionsDiv = document.createElement('div');
                            actionsDiv.className = 'utility-tools-email-message-actions';
                            actionsDiv.style.cssText = 'display: flex; gap: 6px; white-space: nowrap;';

                            const deleteBtn = document.createElement('button');
                            deleteBtn.className = 'utility-tools-email-delete-btn';
                            deleteBtn.style.cssText = 'padding: 3px 6px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: 500; min-width: 30px; text-align: center; box-shadow: 0 1px 2px rgba(0,0,0,0.1);';
                            deleteBtn.textContent = '删除';

                            actionsDiv.appendChild(deleteBtn);

                            // 更改DOM结构，将时间显示放在主题后面
                            headerDiv.appendChild(subjectDiv);
                            headerDiv.appendChild(timeDiv);
                            headerDiv.appendChild(actionsDiv);

                            messageDiv.appendChild(headerDiv);
                            emailsList.appendChild(messageDiv);

                            // 查看邮件内容
                            subjectDiv.addEventListener('click', async () => {
                                try {
                                    const detail = await mailClient.getMessage(email, msg.id);
                                    const content = detail.body?.text || detail.body?.html || '无内容';
                                    // 创建一个更好的内容显示弹窗
                                    const contentModal = document.createElement('div');
                                    contentModal.className = 'utility-tools-email-content-modal';
                                    contentModal.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 0; border-radius: 6px; max-width: 450px; max-height: 70vh; overflow: hidden; z-index: 2147483647; box-shadow: 0 8px 20px rgba(0,0,0,0.2); border: 1px solid #e0e0e0; width: 80%;';

                                    contentModal.innerHTML = `
                                        <div class="utility-tools-email-content-header" style="background-color: #3f51b5; color: white; padding: 10px 15px; font-size: 15px; font-weight: 600; line-height: 1.3;">
                                            <strong>主题：</strong>${msg.subject || '(无主题)'}
                                        </div>
                                        <div class="utility-tools-email-content-time" style="padding: 6px 15px; color: #757575; font-size: 12px; font-weight: 500; border-bottom: 1px solid #e0e0e0;">
                                            <strong>时间：</strong>${msg.date ? formatDateTime(msg.date) : '未知时间'}
                                        </div>
                                        <div class="utility-tools-email-content-body" style="padding: 15px; max-height: 45vh; overflow-y: auto; white-space: pre-wrap; line-height: 1.5; color: #212121; font-size: 13px;">${content}</div>
                                        <button class="utility-tools-email-content-close" style="position: absolute; top: 8px; right: 8px; padding: 4px 8px; background: rgba(0,0,0,0.1); color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 14px; font-weight: bold;">×</button>
                                    `;
                                    document.body.appendChild(contentModal);

                                    contentModal.querySelector('.utility-tools-email-content-close').onclick = () => {
                                        contentModal.remove();
                                    };
                                } catch (e) {
                                    console.error('获取邮件详情失败:', e);
                                    alert('获取邮件详情失败');
                                }
                            });

                            // 删除邮件
                            deleteBtn.addEventListener('click', async () => {
                                try {
                                    await mailClient.deleteMessage(email, msg.id);
                                    messageDiv.remove();
                                } catch (e) {
                                    console.error('删除邮件失败:', e);
                                    alert('删除邮件失败');
                                }
                            });
                        }
                    } else {
                        emailsList.innerHTML = '<div class="utility-tools-email-empty" style="padding: 10px; text-align: center; color: #757575; font-size: 13px; background-color: #f5f5f5; border-radius: 8px; margin: 10px auto;">无邮件</div>';
                    }
                } catch (e) {
                    console.error('获取邮件列表失败:', e);
                    emailsList.innerHTML = `<div class="utility-tools-email-error" style="padding: 10px; text-align: center; color: #f44336; font-size: 13px; background-color: #ffebee; border-radius: 8px; margin: 10px auto; border-left: 3px solid #f44336;">获取失败</div>`;
                } finally {
                    // 无论成功或失败，都移除刷新动画
                    refreshEmailsBtn.classList.remove('refreshing');
                }
            }

            // 绑定刷新按钮事件
            refreshEmailsBtn.addEventListener('click', refreshMessages);

            // 复制邮箱按钮事件
            document.getElementById('copy-email').addEventListener('click', () => {
                const email = emailInput.value.trim();
                if (!email) {
                    alert('请先输入邮箱地址');
                    return;
                }

                // 直接复制邮箱地址
                try {
                    navigator.clipboard.writeText(email).then(() => {
                        // 显示反馈
                        const copyBtn = document.getElementById('copy-email');
                        const originalText = copyBtn.textContent;
                        copyBtn.textContent = '已复制!';
                        setTimeout(() => {
                            copyBtn.textContent = originalText;
                        }, 1000);
                    });
                } catch (err) {
                    // 如果现代API失败，回退到旧方法
                    emailInput.select();
                    try {
                        document.execCommand('copy');
                        // 显示反馈
                        const copyBtn = document.getElementById('copy-email');
                        const originalText = copyBtn.textContent;
                        copyBtn.textContent = '已复制!';
                        setTimeout(() => {
                            copyBtn.textContent = originalText;
                        }, 1000);
                    } catch (e) {
                        console.error('复制失败:', e);
                        alert('复制失败，请手动复制');
                    }
                }
            });

            // 清空邮箱按钮事件
            clearMailboxBtn.addEventListener('click', async () => {
                const email = emailInput.value.trim();
                if (!email) {
                    alert('请输入邮箱地址');
                    return;
                }

                if (confirm('确定要清空邮箱吗？此操作不可恢复！')) {
                    try {
                        await mailClient.deleteMailbox(email);
                        emailsList.innerHTML = '<div class="utility-tools-email-empty">已清空</div>';
                    } catch (e) {
                        console.error('清空邮箱失败:', e);
                        alert('清空邮箱失败');
                    }
                }
            });
        }

        // 初始化身份生成功能
        function initializeIdentityGenerator() {
            console.log('初始化身份生成功能');

            // 获取身份生成相关元素
            const generateIdentityBtn = document.getElementById('generate-identity');
            const copyAllIdentityBtn = document.getElementById('copy-all-identity');
            const identityResult = document.getElementById('identity-result');

            // 初始化时禁用复制所有信息按钮
            copyAllIdentityBtn.disabled = true;
            copyAllIdentityBtn.style.opacity = '0.5';

            // 生成美国身份信息
            async function generateIdentity() {
                // 显示加载状态
                identityResult.innerHTML = '<div class="utility-tools-identity-loading" style="padding: 10px; text-align: center; color: #757575; font-size: 13px; background-color: #f5f5f5; border-radius: 8px; margin: 10px auto;">正在生成身份信息...</div>';

                try {
                    // 使用RandomUser.me API获取美国个人信息
                    const response = await new Promise((resolve, reject) => {
                        console.log('开始请求RandomUser.me API...');
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: 'https://randomuser.me/api/?nat=us&inc=name,login,location,phone,dob',
                            timeout: 10000,
                            onload: function(response) {
                                console.log('API响应状态:', response.status);
                                console.log('API响应内容:', response.responseText.substring(0, 100) + '...');
                                if (response.status === 200) {
                                    resolve(response);
                                } else {
                                    reject(new Error(`API响应错误: ${response.status}`));
                                }
                            },
                            onerror: function(error) {
                                console.error('API请求错误:', error);
                                reject(new Error('API请求失败'));
                            },
                            ontimeout: function() {
                                console.error('API请求超时');
                                reject(new Error('API请求超时'));
                            }
                        });
                    });

                    console.log('解析API响应数据...');
                    const data = JSON.parse(response.responseText).results[0];
                    console.log('解析成功:', data);

                    // 生成密码
                    const password = generatePassword(12, true);

                    // 构建身份信息对象
                    const identity = {
                        fullName: `${data.name.first} ${data.name.last}`,
                        username: data.login.username,
                        password: password,
                        streetAddress: `${data.location.street.number} ${data.location.street.name}`,
                        city: data.location.city,
                        state: `${data.location.state} (${data.location.state.substring(0, 2).toUpperCase()})`,
                        zipCode: data.location.postcode.toString(),
                        phoneNumber: data.phone
                    };

                    console.log('生成的身份信息:', identity);

                    // 显示生成的身份信息
                    displayIdentity(identity);

                    // 启用复制所有信息按钮
                    copyAllIdentityBtn.disabled = false;
                    copyAllIdentityBtn.style.opacity = '1';
                } catch (error) {
                    console.error('生成身份信息失败:', error);
                    identityResult.innerHTML = `<div class="utility-tools-identity-error" style="padding: 10px; text-align: center; color: #f44336; font-size: 13px; background-color: #ffebee; border-radius: 8px; margin: 10px auto; border-left: 3px solid #f44336;">生成失败: ${error.message}</div>`;

                    // 显示错误信息，不使用备用方案
                    console.error('生成身份信息失败，请稍后再试');
                }
            }

            // 生成随机密码
            function generatePassword(length = 12, includeSpecial = true) {
                const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                const specialChars = "!@#$%^&*()_+-=";
                const allChars = includeSpecial ? chars + specialChars : chars;

                let password = '';
                for (let i = 0; i < length; i++) {
                    password += allChars.charAt(Math.floor(Math.random() * allChars.length));
                }
                return password;
            }

            // 显示生成的身份信息
            function displayIdentity(identity) {
                // 清空之前的结果
                identityResult.innerHTML = '';

                // 定义要显示的字段和标签
                const fields = [
                    { key: 'fullName', label: '姓名' },
                    { key: 'username', label: '用户名' },
                    { key: 'password', label: '密码' },
                    { key: 'streetAddress', label: '地址' },
                    { key: 'city', label: '城市' },
                    { key: 'state', label: '州/省' },
                    { key: 'zipCode', label: '邮编' },
                    { key: 'phoneNumber', label: '电话' }
                ];

                // 为每个字段创建一个带复制按钮的项
                fields.forEach(field => {
                    const value = identity[field.key];
                    if (value) {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'utility-tools-identity-item';
                        itemDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin-bottom: 1px; padding: 2px 3px; background-color: #f8f9fa; border-radius: 2px; border: 1px solid #eee; min-height: 20px;';

                        const labelSpan = document.createElement('span');
                        labelSpan.className = 'utility-tools-identity-label';
                        labelSpan.style.cssText = 'font-weight: bold; color: #555; width: 50px; font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';
                        labelSpan.textContent = `${field.label}：`;

                        const valueSpan = document.createElement('span');
                        valueSpan.className = 'utility-tools-identity-value';
                        valueSpan.style.cssText = 'flex-grow: 1; margin: 0 3px; word-break: break-all; font-size: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';
                        valueSpan.textContent = value;

                        const copyBtn = document.createElement('button');
                        copyBtn.className = 'utility-tools-copy-btn';
                        copyBtn.style.cssText = 'flex-shrink: 0; min-width: 40px; display: flex; align-items: center; justify-content: center; font-size: 12px; padding: 2px 5px; height: 20px; line-height: 1; border-radius: 4px; background-color: #3f51b5; color: white; border: none;';
                        copyBtn.textContent = '复制';
                        copyBtn.setAttribute('data-value', value);

                        itemDiv.appendChild(labelSpan);
                        itemDiv.appendChild(valueSpan);
                        itemDiv.appendChild(copyBtn);
                        identityResult.appendChild(itemDiv);

                        // 添加复制按钮事件
                        copyBtn.addEventListener('click', () => {
                            copyIdentityValue(value, copyBtn);
                        });
                    }
                });

                // 获取复制所有信息按钮
                const copyAllBtn = document.getElementById('copy-all-identity');

                // 添加复制所有信息按钮事件
                copyAllBtn.addEventListener('click', () => {
                    // 收集所有信息
                    const allText = fields.map(field => {
                        const value = identity[field.key];
                        return value ? `${field.label}：${value}` : '';
                    }).filter(text => text).join('\n');

                    copyIdentityValue(allText, copyAllBtn);
                });
            }

            // 复制身份信息值
            function copyIdentityValue(text, button) {
                navigator.clipboard.writeText(text).then(() => {
                    // 显示反馈
                    const originalText = button.textContent;
                    button.textContent = '已复制!';
                    setTimeout(() => {
                        button.textContent = originalText;
                    }, 1000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
            }

            // 绑定生成按钮事件
            generateIdentityBtn.addEventListener('click', async () => {
                // 显示加载状态
                const originalText = generateIdentityBtn.textContent;
                generateIdentityBtn.textContent = '生成中...';
                generateIdentityBtn.disabled = true;
                generateIdentityBtn.style.opacity = '0.7';

                try {
                    await generateIdentity();
                } finally {
                    // 无论成功失败，都恢复按钮状态
                    generateIdentityBtn.textContent = originalText;
                    generateIdentityBtn.disabled = false;
                    generateIdentityBtn.style.opacity = '1';
                }
            });
        }
    }

    // 将面板定位在按钮附近，并避免超出屏幕
    function positionPanelNearButton(button, panel) {
        // 获取按钮位置
        const buttonRect = button.getBoundingClientRect();

        // 获取面板的实际尺寸（从CSS中获取）
        const panelWidth = 420; // 与CSS中定义的宽度一致
        const panelHeight = 400; // 估计高度

        // 添加安全边距，确保面板不会紧贴屏幕边缘
        const safeMargin = 20;

        // 屏幕尺寸
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        // 检查按钮是否靠近屏幕右侧
        const isButtonNearRightEdge = buttonRect.right > screenWidth - 100;

        // 根据按钮位置智能决定面板的最佳位置
        let left, top;

        // 水平位置计算
        if (isButtonNearRightEdge) {
            // 如果按钮靠近右侧边缘，将面板放在按钮的左侧
            left = buttonRect.left - panelWidth - safeMargin;

            // 如果左侧空间不足，则将面板放在按钮的左侧但不超出屏幕
            if (left < safeMargin) {
                // 尝试将面板放在按钮的正上方或下方，水平居中
                left = Math.max(safeMargin, Math.min(screenWidth - panelWidth - safeMargin,
                                buttonRect.left + (buttonRect.width / 2) - (panelWidth / 2)));
            }
        } else {
            // 如果按钮不靠近右侧边缘，尝试将面板放在按钮的右侧
            left = buttonRect.right + safeMargin;

            // 如果右侧空间不足，则将面板放在按钮的左侧
            if (left + panelWidth > screenWidth - safeMargin) {
                left = buttonRect.left - panelWidth - safeMargin;

                // 如果左侧也放不下，则水平居中显示
                if (left < safeMargin) {
                    left = Math.max(safeMargin, (screenWidth - panelWidth) / 2);
                }
            }
        }

        // 垂直位置计算
        // 尝试将面板垂直居中于按钮
        top = buttonRect.top + (buttonRect.height / 2) - (panelHeight / 2);

        // 确保面板不超出上边界
        if (top < safeMargin) {
            top = safeMargin;
        }

        // 确保面板不超出下边界
        if (top + panelHeight > screenHeight - safeMargin) {
            top = screenHeight - panelHeight - safeMargin;
        }

        // 设置面板位置
        panel.style.position = 'fixed';
        panel.style.left = left + 'px';
        panel.style.top = top + 'px';
        panel.style.bottom = 'auto';
        panel.style.right = 'auto';

        // 添加过渡效果，使位置变化更平滑
        panel.style.transition = 'left 0.3s ease, top 0.3s ease';
    }

    // 使元素可拖动
    function makeDraggable(element, handle) {
        // 初始鼠标位置和元素位置的偏移量
        let offsetX = 0;
        let offsetY = 0;
        let isDragging = false;
        let hasMoved = false;
        let startX = 0;
        let startY = 0;
        const DRAG_THRESHOLD = 5; // 拖动阈值，移动超过这个距离才算拖动

        // 如果是按钮，不设置光标样式，保持原有样式
        if (handle !== element || handle.id !== 'utility-tools-toggle') {
            handle.style.cursor = 'move';
        }

        // 确保元素使用绝对或固定定位
        if (window.getComputedStyle(element).position === 'static') {
            element.style.position = 'fixed';
        }

        // 鼠标按下事件
        handle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 记录起始位置
            startX = e.clientX;
            startY = e.clientY;
            hasMoved = false;

            // 计算鼠标位置与元素位置的偏移量
            const rect = element.getBoundingClientRect();
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;

            isDragging = true;

            // 添加鼠标移动和释放事件
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);

            // 添加一个类来表示正在拖动
            element.classList.add('dragging');
        });

        // 鼠标移动事件处理函数
        function onMouseMove(e) {
            if (!isDragging) return;

            e.preventDefault();

            // 计算移动距离
            const dx = Math.abs(e.clientX - startX);
            const dy = Math.abs(e.clientY - startY);

            // 如果移动距离超过阈值，标记为已移动
            if (dx > DRAG_THRESHOLD || dy > DRAG_THRESHOLD) {
                hasMoved = true;
            }

            // 计算新位置 - 直接使用鼠标位置减去偏移量
            let newLeft = e.clientX - offsetX;
            let newTop = e.clientY - offsetY;

            // 确保元素不会被拖出屏幕
            const maxX = window.innerWidth - element.offsetWidth;
            const maxY = window.innerHeight - element.offsetHeight;

            newLeft = Math.max(0, Math.min(newLeft, maxX));
            newTop = Math.max(0, Math.min(newTop, maxY));

            // 设置元素的新位置
            element.style.left = newLeft + 'px';
            element.style.top = newTop + 'px';

            // 如果是切换按钮，重置其他位置属性
            if (element.id === 'utility-tools-toggle') {
                element.style.bottom = 'auto';
                element.style.right = 'auto';
            }
        }

        // 鼠标释放事件处理函数
        function onMouseUp(e) {
            isDragging = false;

            // 移除事件监听器
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);

            // 移除拖动类
            element.classList.remove('dragging');

            // 如果是切换按钮，保存其位置
            if (element.id === 'utility-tools-toggle') {
                CONFIG.buttonPosition = {
                    top: element.style.top,
                    left: element.style.left,
                    bottom: 'auto',
                    right: 'auto'
                };
                saveConfig(CONFIG);

                // 如果拖动了，阻止点击事件
                if (hasMoved) {
                    e.preventDefault();
                    e.stopPropagation();
                    // 设置标记表示这是拖动后的点击
                    element.setAttribute('data-was-dragged', 'true');
                    // 一段时间后移除标记，以防止永久阻止点击
                    setTimeout(() => {
                        element.removeAttribute('data-was-dragged');
                    }, 300);
                }
            }
        }
    }

    // 复制文本到剪贴板
    async function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        element.select();

        try {
            // 使用现代剪贴板 API
            await navigator.clipboard.writeText(element.value);

            // 显示反馈
            const originalValue = element.value;
            element.value = '已复制!';
            setTimeout(() => {
                element.value = originalValue;
            }, 1000);
        } catch (err) {
            // 如果现代API失败，回退到旧方法
            try {
                document.execCommand('copy');

                // 显示反馈
                const originalValue = element.value;
                element.value = '已复制!';
                setTimeout(() => {
                    element.value = originalValue;
                }, 1000);
            } catch (e) {
                console.error('复制失败:', e);
            }
        }
    }

    // ==================== 样式 ====================

    function addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            /* 基础变量 - 加入!important确保应用 */
            #utility-tools-container {
                --primary-color: #3f51b5 !important;
                --primary-light: #757de8 !important;
                --primary-dark: #002984 !important;
                --secondary-color: #4caf50 !important;
                --secondary-light: #80e27e !important;
                --secondary-dark: #087f23 !important;
                --danger-color: #f44336 !important;
                --text-on-primary: #ffffff !important;
                --text-primary: #212121 !important;
                --text-secondary: #757575 !important;
                --background-color: #ffffff !important;
                --surface-color: #f5f5f5 !important;
                --border-color: #e0e0e0 !important;
                --shadow-color: rgba(0, 0, 0, 0.1) !important;
                --shadow-color-dark: rgba(0, 0, 0, 0.2) !important;
                --border-radius: 8px !important;
                --transition-speed: 0.3s !important;
            }

            /* 强制应用基础样式到所有工具元素 */
            #utility-tools-container * {
                box-sizing: border-box !important;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                line-height: normal !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* 容器样式 */
            #utility-tools-container {
                position: fixed !important;
                z-index: 2147483647 !important; /* 最大z-index值 */
                bottom: 20px !important;
                right: 20px !important;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-size: 14px !important;
                color: var(--text-primary) !important;
                line-height: 1.5 !important;
                width: auto !important;
                height: auto !important;
            }

            /* 工具按钮样式 */
            #utility-tools-toggle {
                position: fixed !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                z-index: 2147483647 !important;
                min-width: 48px !important;
                min-height: 48px !important;
                width: 48px !important;
                height: 48px !important;
                padding: 0 !important;
                margin: 0 !important;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-size: 22px !important;
                line-height: 48px !important;
                text-align: center !important;
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
            }

            #utility-tools-toggle.dragging {
                transform: scale(1.1) !important;
                box-shadow: 0 4px 12px var(--shadow-color-dark) !important;
                opacity: 0.9 !important;
            }

            #utility-tools-toggle:hover {
                background-color: var(--primary-light) !important;
                transform: scale(1.05) !important;
                box-shadow: 0 4px 15px var(--shadow-color-dark) !important;
            }

            #utility-tools-toggle:active {
                transform: scale(0.95) !important;
                background-color: var(--primary-dark) !important;
            }

            /* 面板样式 */
            #utility-tools-panel {
                position: fixed !important;
                width: 420px !important;
                background-color: var(--background-color) !important;
                border-radius: var(--border-radius) !important;
                box-shadow: 0 4px 20px var(--shadow-color) !important;
                overflow: hidden !important;
                transition: all var(--transition-speed) ease !important;
                border: 1px solid var(--border-color) !important;
                max-width: calc(100vw - 40px) !important; /* 响应式设计 */
                z-index: 2147483646 !important; /* 比按钮低1，但比其他元素高 */
            }

            #utility-tools-panel.dragging {
                opacity: 0.95 !important;
                box-shadow: 0 8px 25px var(--shadow-color-dark) !important;
            }

            /* 头部样式 */
            #utility-tools-panel .utility-tools-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                padding: 12px 16px !important;
                background-color: var(--primary-color) !important;
                color: var(--text-on-primary) !important;
                font-weight: 600 !important;
                font-size: 16px !important;
                letter-spacing: 0.5px !important;
                box-shadow: 0 2px 4px var(--shadow-color) !important;
                position: relative !important;
            }

            #utility-tools-panel .utility-tools-header::after {
                content: '' !important;
                position: absolute !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                height: 2px !important;
                background: linear-gradient(to right, var(--primary-light), var(--secondary-light)) !important;
            }

            #utility-tools-panel .utility-tools-header-buttons {
                display: flex !important;
                gap: 8px !important;
            }

            #utility-tools-panel .utility-tools-header button {
                background: none !important;
                border: none !important;
                color: var(--text-on-primary) !important;
                font-size: 18px !important;
                cursor: pointer !important;
                padding: 0 !important;
                width: 28px !important;
                height: 28px !important;
                border-radius: 4px !important;
                transition: background-color 0.2s ease !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #utility-tools-panel #utility-tools-settings {
                font-size: 16px !important;
            }

            #utility-tools-panel #utility-tools-close {
                font-size: 20px !important;
            }

            #utility-tools-panel .utility-tools-header button:hover {
                background-color: rgba(255, 255, 255, 0.2) !important;
            }

            #utility-tools-panel .utility-tools-header button:active {
                background-color: rgba(255, 255, 255, 0.3) !important;
            }

            /* 标签页样式 */
            #utility-tools-panel .utility-tools-tabs {
                display: flex !important;
                background-color: var(--surface-color) !important;
                border-bottom: 1px solid var(--border-color) !important;
                padding: 0 4px !important;
            }

            #utility-tools-panel .utility-tools-tab-btn {
                flex: 1 !important;
                padding: 14px 10px !important;
                text-align: center !important;
                background: none !important;
                border: none !important;
                cursor: pointer !important;
                transition: all var(--transition-speed) !important;
                font-size: 14px !important;
                color: var(--text-secondary) !important;
                border-bottom: 2px solid transparent !important;
                white-space: nowrap !important;
                position: relative !important;
                overflow: hidden !important;
                font-weight: 500 !important;
                letter-spacing: 0.3px !important;
            }

            #utility-tools-panel .utility-tools-tab-btn::after {
                content: '' !important;
                position: absolute !important;
                bottom: 0 !important;
                left: 50% !important;
                width: 0 !important;
                height: 2px !important;
                background-color: var(--primary-color) !important;
                transition: all 0.3s ease !important;
                transform: translateX(-50%) !important;
            }

            #utility-tools-panel .utility-tools-tab-btn:hover {
                background-color: rgba(0, 0, 0, 0.05) !important;
                color: var(--primary-color) !important;
            }

            #utility-tools-panel .utility-tools-tab-btn:hover::after {
                width: 40% !important;
            }

            #utility-tools-panel .utility-tools-tab-btn.active {
                background-color: var(--background-color) !important;
                color: var(--primary-color) !important;
                font-weight: 600 !important;
            }

            #utility-tools-panel .utility-tools-tab-btn.active::after {
                width: 100% !important;
                background-color: var(--primary-color) !important;
            }

            /* 内容区样式 */
            #utility-tools-panel .utility-tools-content {
                padding: 18px !important;
                height: 300px !important;
                overflow-y: auto !important;
                background-color: var(--background-color) !important;
                scrollbar-width: thin !important;
                scrollbar-color: var(--primary-light) var(--surface-color) !important;
            }

            #utility-tools-panel .utility-tools-content::-webkit-scrollbar {
                width: 8px !important;
            }

            #utility-tools-panel .utility-tools-content::-webkit-scrollbar-track {
                background: var(--surface-color) !important;
                border-radius: 4px !important;
            }

            #utility-tools-panel .utility-tools-content::-webkit-scrollbar-thumb {
                background-color: var(--primary-light) !important;
                border-radius: 4px !important;
                border: 2px solid var(--surface-color) !important;
            }

            #utility-tools-panel .utility-tools-section {
                padding-bottom: 8px !important;
                height: 100% !important;
                display: flex !important;
                flex-direction: column !important;
                animation: fadeIn 0.3s ease !important;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(5px); }
                to { opacity: 1; transform: translateY(0); }
            }

            #utility-tools-panel .utility-tools-tab-content {
                display: none !important;
            }

            #utility-tools-panel .utility-tools-tab-content.active {
                display: block !important;
            }

            /* 输入区域样式 */
            #utility-tools-panel .utility-tools-input-group {
                margin-bottom: 16px !important;
                position: relative !important;
            }

            #utility-tools-panel .utility-tools-options {
                margin-bottom: 12px !important;
                background-color: var(--surface-color) !important;
                padding: 12px !important;
                border-radius: var(--border-radius) !important;
                box-shadow: 0 1px 3px var(--shadow-color) !important;
            }

            #utility-tools-panel .utility-tools-options label {
                display: block !important;
                margin-bottom: 8px !important;
                font-weight: 500 !important;
                color: var(--text-primary) !important;
            }

            #utility-tools-panel .utility-tools-input-group input[type="text"],
            #utility-tools-panel .utility-tools-input-group input[type="number"],
            #utility-tools-panel .utility-tools-input-group textarea,
            #utility-tools-panel .utility-tools-settings-group textarea {
                width: 100% !important;
                padding: 10px 12px !important;
                border: 1px solid var(--border-color) !important;
                border-radius: var(--border-radius) !important;
                margin-bottom: 10px !important;
                box-sizing: border-box !important;
                font-family: inherit !important;
                font-size: 14px !important;
                color: var(--text-primary) !important;
                transition: all 0.2s ease !important;
                box-shadow: 0 1px 3px var(--shadow-color) !important;
                outline: none !important;
            }

            #utility-tools-panel .utility-tools-input-group input[type="text"]:focus,
            #utility-tools-panel .utility-tools-input-group input[type="number"]:focus,
            #utility-tools-panel .utility-tools-input-group textarea:focus,
            #utility-tools-panel .utility-tools-settings-group textarea:focus {
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2) !important;
            }

            #utility-tools-panel .utility-tools-input-group textarea,
            #utility-tools-panel .utility-tools-settings-group textarea {
                min-height: 80px !important;
                resize: vertical !important;
                line-height: 1.5 !important;
            }

            #utility-tools-panel .utility-tools-button-group {
                display: flex !important;
                gap: 10px !important;
                margin-top: 5px !important;
            }

            #utility-tools-panel .utility-tools-input-group button,
            #utility-tools-panel .utility-tools-copy-btn,
            #utility-tools-panel .utility-tools-save-btn {
                padding: 10px 16px !important;
                background-color: var(--primary-color) !important;
                color: var(--text-on-primary) !important;
                border: none !important;
                border-radius: var(--border-radius) !important;
                cursor: pointer !important;
                transition: all 0.2s ease !important;
                font-weight: 500 !important;
                letter-spacing: 0.3px !important;
                box-shadow: 0 2px 4px var(--shadow-color) !important;
                position: relative !important;
                overflow: hidden !important;
                text-align: center !important;
            }

            #utility-tools-panel .utility-tools-input-group button::after,
            #utility-tools-panel .utility-tools-copy-btn::after,
            #utility-tools-panel .utility-tools-save-btn::after {
                content: '' !important;
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                width: 5px !important;
                height: 5px !important;
                background: rgba(255, 255, 255, 0.5) !important;
                opacity: 0 !important;
                border-radius: 100% !important;
                transform: scale(1, 1) translate(-50%, -50%) !important;
                transform-origin: 50% 50% !important;
            }

            #utility-tools-panel .utility-tools-input-group button:active::after,
            #utility-tools-panel .utility-tools-copy-btn:active::after,
            #utility-tools-panel .utility-tools-save-btn:active::after {
                animation: ripple 0.4s ease-out !important;
            }

            @keyframes ripple {
                0% {
                    transform: scale(0, 0);
                    opacity: 0.5;
                }
                100% {
                    transform: scale(20, 20);
                    opacity: 0;
                }
            }

            #utility-tools-panel .utility-tools-primary-btn {
                background-color: var(--primary-color) !important;
                padding: 10px 18px !important;
                font-size: 14px !important;
            }

            #utility-tools-panel .utility-tools-input-group button:hover,
            #utility-tools-panel .utility-tools-copy-btn:hover,
            #utility-tools-panel .utility-tools-save-btn:hover {
                background-color: var(--primary-light) !important;
                box-shadow: 0 4px 8px var(--shadow-color) !important;
                transform: translateY(-1px) !important;
            }

            #utility-tools-panel .utility-tools-input-group button:active,
            #utility-tools-panel .utility-tools-copy-btn:active,
            #utility-tools-panel .utility-tools-save-btn:active {
                background-color: var(--primary-dark) !important;
                box-shadow: 0 1px 2px var(--shadow-color) !important;
                transform: translateY(1px) !important;
            }

            #utility-tools-panel .utility-tools-result {
                display: flex !important;
                align-items: center !important;
                gap: 10px !important;
                margin-top: 5px !important;
            }

            #utility-tools-panel .utility-tools-result input,
            #utility-tools-panel .utility-tools-result textarea {
                flex: 1 !important;
                padding: 10px 12px !important;
                border: 1px solid var(--border-color) !important;
                border-radius: var(--border-radius) !important;
                background-color: var(--surface-color) !important;
                box-sizing: border-box !important;
                font-family: inherit !important;
                color: var(--text-primary) !important;
                transition: all 0.2s ease !important;
                box-shadow: 0 1px 3px var(--shadow-color) !important;
                outline: none !important;
            }

            #utility-tools-panel .utility-tools-result input:focus,
            #utility-tools-panel .utility-tools-result textarea:focus {
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2) !important;
            }

            #utility-tools-panel .utility-tools-result textarea {
                min-height: 80px !important;
                height: 100px !important;
                resize: none !important;
                line-height: 1.5 !important;
                font-size: 14px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            #utility-tools-panel .utility-tools-input-group textarea {
                height: 90px !important;
                resize: none !important;
                line-height: 1.5 !important;
                font-size: 14px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 为 Base64 页面的文本框添加特殊样式 */
            #utility-tools-panel #content-base64 .utility-tools-input-group textarea,
            #utility-tools-panel #content-base64 .utility-tools-result textarea {
                height: 80px !important;
            }

            #utility-tools-panel .utility-tools-copy-btn {
                flex-shrink: 0 !important;
                min-width: 70px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #utility-tools-panel .utility-tools-copy-btn::before {
                content: '📋' !important;
                margin-right: 5px !important;
                font-size: 14px !important;
            }

            /* 设置面板样式 */
            #utility-tools-settings-panel {
                position: fixed !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                width: 420px !important;
                padding: 0 !important;
                background-color: var(--background-color) !important;
                border-radius: var(--border-radius) !important;
                box-shadow: 0 10px 30px var(--shadow-color-dark) !important;
                z-index: 2147483647 !important;
                max-height: 80vh !important;
                overflow-y: auto !important;
                border: 1px solid var(--border-color) !important;
                animation: fadeInScale 0.3s ease !important;
                max-width: calc(100vw - 40px) !important; /* 响应式设计 */
            }

            @keyframes fadeInScale {
                from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
                to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }

            #utility-tools-settings-panel .utility-tools-settings-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                padding: 15px 20px !important;
                background-color: var(--primary-color) !important;
                color: var(--text-on-primary) !important;
                border-top-left-radius: var(--border-radius) !important;
                border-top-right-radius: var(--border-radius) !important;
                margin-bottom: 0 !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-header h3 {
                margin: 0 !important;
                font-size: 18px !important;
                color: var(--text-on-primary) !important;
                font-weight: 600 !important;
                letter-spacing: 0.5px !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-close {
                background: none !important;
                border: none !important;
                font-size: 24px !important;
                cursor: pointer !important;
                color: var(--text-on-primary) !important;
                padding: 0 5px !important;
                transition: all 0.2s ease !important;
                border-radius: 4px !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-close:hover {
                background-color: rgba(255, 255, 255, 0.2) !important;
                transform: scale(1.1) !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-close:active {
                transform: scale(0.95) !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-group {
                margin-bottom: 20px !important;
                padding: 0 20px !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-group:first-of-type {
                padding-top: 20px !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-group:last-of-type {
                padding-bottom: 20px !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-group label {
                display: block !important;
                margin-bottom: 8px !important;
                font-weight: 500 !important;
                color: var(--text-primary) !important;
            }

            #utility-tools-settings-panel .utility-tools-settings-group:last-child {
                margin-bottom: 0 !important;
                text-align: right !important;
                border-top: 1px solid var(--border-color) !important;
                padding-top: 15px !important;
                background-color: var(--surface-color) !important;
                border-bottom-left-radius: var(--border-radius) !important;
                border-bottom-right-radius: var(--border-radius) !important;
            }

            #utility-tools-settings-panel .utility-tools-text-input {
                width: 100% !important;
                padding: 10px 12px !important;
                border: 1px solid var(--border-color) !important;
                border-radius: var(--border-radius) !important;
                box-sizing: border-box !important;
                font-size: 14px !important;
                font-family: inherit !important;
                transition: all 0.2s ease !important;
                box-shadow: 0 1px 3px var(--shadow-color) !important;
                outline: none !important;
            }

            #utility-tools-settings-panel .utility-tools-text-input:focus {
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2) !important;
            }

            /* 身份生成相关样式 */
            #utility-tools-panel .utility-tools-identity-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 3px !important; /* 进一步减小间距 */
                margin: 0 !important; /* 移除所有外边距 */
                padding: 0 !important; /* 移除所有内边距 */
                flex-grow: 0 !important;
            }

            #utility-tools-panel .utility-tools-identity-result {
                max-height: 250px !important; /* 增加高度 */
                overflow-y: auto !important;
                padding: 0 !important; /* 移除内边距 */
                background-color: #f9f9f9 !important;
                border-radius: 3px !important;
                border: 1px solid #e0e0e0 !important;
                margin: 0 !important; /* 移除外边距 */
            }

            #utility-tools-panel .utility-tools-identity-empty {
                padding: 5px !important; /* 进一步减小内边距 */
                text-align: center !important;
                color: #757575 !important;
                font-size: 11px !important; /* 进一步减小字体 */
            }

            #utility-tools-panel .utility-tools-identity-loading {
                padding: 5px !important; /* 进一步减小内边距 */
                text-align: center !important;
                color: #757575 !important;
                font-size: 11px !important; /* 进一步减小字体 */
                background-color: #f5f5f5 !important;
            }

            #utility-tools-panel .utility-tools-identity-error {
                padding: 4px !important; /* 进一步减小内边距 */
                text-align: center !important;
                color: #f44336 !important;
                font-size: 10px !important; /* 进一步减小字体 */
                background-color: #ffebee !important;
                border-radius: 3px !important; /* 进一步减小圆角 */
                margin: 2px auto !important; /* 进一步减小外边距 */
                border-left: 1px solid #f44336 !important; /* 进一步减小边框 */
            }

            #utility-tools-panel .utility-tools-identity-item {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                margin-bottom: 1px !important; /* 进一步减小底部间距 */
                padding: 2px 3px !important;
                background-color: #f8f9fa !important;
                border-radius: 2px !important;
                border: 1px solid #eee !important;
                min-height: 20px !important; /* 减小高度 */
            }

            #utility-tools-panel .utility-tools-identity-label {
                font-weight: bold !important;
                color: #555 !important;
                width: 50px !important; /* 增加标签宽度 */
                font-size: 12px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }

            #utility-tools-panel .utility-tools-identity-value {
                flex-grow: 1 !important;
                margin: 0 3px !important;
                word-break: break-all !important;
                font-size: 12px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }

            /* 邮箱相关样式 */
            #utility-tools-panel .utility-tools-email-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 8px !important; /* 减小间距 */
                margin-bottom: 8px !important; /* 减小底部间距 */
                flex-grow: 0 !important;
            }

            /* 刷新和清空按钮 */
            #utility-tools-panel .utility-tools-email-toolbar {
                display: flex !important;
                justify-content: flex-end !important;
                gap: 10px !important;
                margin-bottom: 12px !important;
            }

            #utility-tools-panel .utility-tools-icon {
                display: block !important;
            }

            #utility-tools-panel #refresh-emails,
            #utility-tools-panel #clear-mailbox {
                width: 36px !important;
                height: 36px !important;
                border-radius: 50% !important;
                padding: 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border: none !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
            }

            #utility-tools-panel #refresh-emails {
                background-color: var(--primary-color) !important;
                color: var(--text-on-primary) !important;
            }

            #utility-tools-panel #clear-mailbox {
                background-color: var(--danger-color) !important;
                color: var(--text-on-primary) !important;
            }

            #utility-tools-panel #refresh-emails svg,
            #utility-tools-panel #clear-mailbox svg {
                display: block !important;
                width: 16px !important;
                height: 16px !important;
            }

            /* 邮箱输入框区域 */
            #utility-tools-panel .utility-tools-email-input-container {
                width: 100% !important;
                position: relative !important;
                margin-bottom: 8px !important;
            }

            #utility-tools-panel .utility-tools-email-input-container input {
                width: 100% !important;
                padding: 10px 12px !important;
                border: 1px solid var(--border-color) !important;
                border-radius: var(--border-radius) !important;
                box-sizing: border-box !important;
                font-family: inherit !important;
                font-size: 14px !important;
                color: var(--text-primary) !important;
                transition: all 0.2s ease !important;
                box-shadow: 0 1px 3px var(--shadow-color) !important;
                outline: none !important;
            }

            #utility-tools-panel .utility-tools-email-input-container input:focus {
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2) !important;
            }

            /* 邮箱类型提示 */
            #utility-tools-panel .utility-tools-email-type-container {
                text-align: right !important;
                margin-bottom: 8px !important;
            }

            #utility-tools-panel .utility-tools-email-hint {
                font-size: 12px !important;
                color: #999 !important;
                margin-top: 5px !important;
                text-align: right !important;
                font-weight: 500 !important;
                letter-spacing: 0.3px !important;
            }

            /* 邮箱按钮容器 */
            #utility-tools-panel .utility-tools-email-buttons-container {
                display: flex !important;
                gap: 10px !important;
                margin-top: 5px !important;
                margin-bottom: 12px !important;
                position: relative !important;
                z-index: 0 !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 邮箱域名组合 */
            #utility-tools-panel .utility-tools-email-domain-group {
                display: flex !important;
                flex-grow: 0 !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
                border-radius: var(--border-radius) !important;
                overflow: hidden !important;
                width: 170px !important;
                min-width: 170px !important;
                position: relative !important;
            }

            /* 添加响应式设计 */
            @media screen and (max-width: 600px) {
                #utility-tools-panel {
                    width: 90vw !important;
                    max-width: 420px !important;
                }

                #utility-tools-panel .utility-tools-email-domain-group {
                    width: 150px !important;
                }

                #utility-tools-panel .utility-tools-button-secondary {
                    width: 50px !important;
                    min-width: 50px !important;
                }

                #utility-tools-panel .utility-tools-domain-select {
                    width: 100px !important;
                }
            }

            /* 修复按钮内文字溢出问题 */
            #utility-tools-panel .utility-tools-button {
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                text-align: center !important;
            }

            /* 修复按钮之间的对齐问题 */
            #utility-tools-panel .utility-tools-button-secondary {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            /* 增加邮箱域名选择器的可见性 */
            #utility-tools-panel .utility-tools-domain-select {
                width: 110px !important;
                border: none !important;
                background-color: #002984 !important;
                color: white !important;
                border-top-right-radius: var(--border-radius) !important;
                border-bottom-right-radius: var(--border-radius) !important;
                cursor: pointer !important;
                padding: 0 8px !important;
                flex-shrink: 0 !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                transition: background-color 0.2s ease !important;
                outline: none !important;
                z-index: 1 !important;
                text-align: center !important;
                text-align-last: center !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
                height: 38px !important;
                line-height: 38px !important;
            }

            #utility-tools-panel .utility-tools-domain-select option {
                background-color: white;
                color: #333;
            }

            /* 额外的样式规则 */

            /* 确保按钮字体和样式统一 */
            #utility-tools-panel button,
            #utility-tools-panel .utility-tools-tab-btn,
            #utility-tools-settings-panel button {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-size: 14px !important;
                text-align: center !important;
                line-height: normal !important;
                box-sizing: border-box !important;
                user-select: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
            }

            /* 修复标签页切换的样式和显示问题 */
            #content-random.active,
            #content-timestamp.active,
            #content-base64.active,
            #content-email.active {
                display: block !important;
            }

            #content-random:not(.active),
            #content-timestamp:not(.active),
            #content-base64:not(.active),
            #content-email:not(.active) {
                display: none !important;
            }

            /* 统一按钮样式 */
            #utility-tools-toggle {
                border-radius: 50% !important;
                background-color: var(--primary-color) !important;
                color: var(--text-on-primary) !important;
                transition: all var(--transition-speed) ease !important;
                border: none !important;
                cursor: pointer !important;
                box-shadow: 0 2px 10px var(--shadow-color) !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                outline: none !important;
            }

            /* 修复随机邮箱按钮的显示问题 */
            #utility-tools-panel #default-email-btn,
            #utility-tools-panel #random-email-btn {
                display: inline-flex !important;
                align-items: center !important;
                justify-content: center !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                height: 38px !important;
                line-height: 38px !important;
            }

            #utility-tools-panel #default-email-btn {
                flex-grow: 1 !important;
                padding: 10px 14px !important;
                background-color: #4caf50 !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: 500 !important;
                text-align: center !important;
            }

            #utility-tools-panel #random-email-btn {
                width: 60px !important;
                min-width: 60px !important;
                background-color: #3f51b5 !important;
                color: white !important;
                border: none !important;
                border-top-right-radius: 0 !important;
                border-bottom-right-radius: 0 !important;
                text-align: center !important;
            }

            /* 确保表单元素样式一致 */
            #utility-tools-panel input[type="text"],
            #utility-tools-panel input[type="number"],
            #utility-tools-panel select,
            #utility-tools-panel textarea {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                font-size: 14px !important;
                line-height: normal !important;
                color: #212121 !important;
            }

            /* 随机字符串相关样式 */
            #utility-tools-panel .utility-tools-random-string-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 10px !important;
                padding: 10px !important;
                border-radius: 6px !important;
                background-color: #f8f9fa !important;
                margin-bottom: 12px !important;
            }

            #utility-tools-panel .utility-tools-random-button-container {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 8px !important;
                margin-top: 8px !important;
            }

            #utility-tools-panel .utility-tools-random-button-container button {
                flex: 1 0 auto !important;
                min-width: 80px !important;
                padding: 8px !important;
                background-color: var(--accent-color) !important;
                color: var(--text-on-accent) !important;
                border: none !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                cursor: pointer !important;
                transition: background-color 0.2s ease !important;
                text-align: center !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }

            #utility-tools-panel .utility-tools-random-button-container button:hover {
                background-color: var(--accent-hover-color) !important;
            }

            /* 搜索引擎选择器相关样式 */
            #utility-tools-panel .utility-tools-search-engine-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 10px !important;
                padding: 10px !important;
                border-radius: 6px !important;
                background-color: #f8f9fa !important;
                margin-bottom: 12px !important;
            }

            #utility-tools-panel .utility-tools-search-engine-buttons {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 8px !important;
                margin-top: 8px !important;
            }

            #utility-tools-panel .utility-tools-search-engine-buttons button {
                flex: 1 0 auto !important;
                min-width: 80px !important;
                padding: 8px !important;
                background-color: var(--accent-color) !important;
                color: var(--text-on-accent) !important;
                border: none !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                cursor: pointer !important;
                transition: background-color 0.2s ease !important;
                text-align: center !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }

            #utility-tools-panel .utility-tools-search-engine-buttons button:hover {
                background-color: var(--accent-hover-color) !important;
            }

            #utility-tools-panel .utility-tools-search-input {
                width: 100% !important;
                padding: 8px 12px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                margin-top: 4px !important;
            }

            /* 正则表达式工具相关样式 */
            #utility-tools-panel .utility-tools-regex-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 10px !important;
                padding: 10px !important;
                border-radius: 6px !important;
                background-color: #f8f9fa !important;
                margin-bottom: 12px !important;
            }

            #utility-tools-panel .utility-tools-regex-input-container,
            #utility-tools-panel .utility-tools-regex-test-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 8px !important;
            }

            #utility-tools-panel .utility-tools-regex-label {
                font-weight: 500 !important;
                margin-bottom: 4px !important;
            }

            #utility-tools-panel .utility-tools-regex-input,
            #utility-tools-panel .utility-tools-regex-test {
                width: 100% !important;
                padding: 8px 12px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                font-size: 14px !important;
                font-family: monospace !important;
            }

            #utility-tools-panel .utility-tools-regex-options {
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 8px !important;
                margin-top: 8px !important;
                margin-bottom: 8px !important;
            }

            #utility-tools-panel .utility-tools-regex-result {
                margin-top: 10px !important;
                padding: 10px !important;
                border-radius: 4px !important;
                background-color: #e9ecef !important;
                font-family: monospace !important;
                white-space: pre-wrap !important;
                max-height: 200px !important;
                overflow-y: auto !important;
            }

            #utility-tools-panel .utility-tools-regex-option {
                display: flex !important;
                align-items: center !important;
                gap: 4px !important;
                font-size: 14px !important;
            }

            /* 邮件列表区域 */
            #utility-tools-panel .utility-tools-emails-list {
                min-height: 30px !important;
                margin-top: 12px !important;
                border-top: 1px solid #e0e0e0 !important;
                padding-top: 12px !important;
            }

            #utility-tools-panel .utility-tools-email-empty {
                padding: 10px !important;
                text-align: center !important;
                color: #757575 !important;
                font-size: 13px !important;
                background-color: #f5f5f5 !important;
                border-radius: 8px !important;
                margin: 10px auto !important;
            }
        `;
        document.head.appendChild(styleElement);
    }

    // ==================== 初始化 ====================

    function init() {
        console.log('QuickTools: 初始化开始');

        try {
            // 添加样式
            addStyles();
            console.log('QuickTools: 样式已添加');

            // 检查当前网站是否应该显示工具
            const shouldShow = shouldShowOnCurrentSite();
            console.log('QuickTools: 当前网站应该显示工具:', shouldShow);

            if (!shouldShow) {
                console.log('QuickTools: 当前网站不在白名单中，不创建UI');
                return; // 如果不在白名单中且不显示在所有网站，则不创建UI
            }

            // 创建UI
            console.log('QuickTools: 开始创建UI');
            const ui = createUI();
            console.log('QuickTools: UI创建完成');

            // 设置事件处理程序
            setupEventHandlers(ui);
            console.log('QuickTools: 事件处理程序设置完成');

            // 生成初始随机字符串
            document.getElementById('random-string-result').value = generateRandomString(10);

            // 注册油猴菜单
            GM_registerMenuCommand('⚙️ QuickTools 设置', () => {
                // 显示面板和设置
                ui.panel.style.display = 'block';
                ui.settingsPanel.style.display = 'block';
            });

            console.log('QuickTools: 初始化完成');

            // 确保图标可见
            setTimeout(() => {
                const toggleButton = document.getElementById('utility-tools-toggle');
                if (toggleButton) {
                    console.log('QuickTools: 图标存在，确保可见');
                    toggleButton.style.display = 'block';
                    toggleButton.style.visibility = 'visible';
                    toggleButton.style.opacity = '1';
                    toggleButton.style.zIndex = '9999';
                } else {
                    console.error('QuickTools: 图标不存在!');
                }
            }, 1000);

        } catch (error) {
            console.error('QuickTools 初始化错误:', error);
        }
    }

    // 确保脚本在页面加载后执行
    if (document.readyState === 'loading') {
        window.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();

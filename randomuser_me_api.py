import requests
import json

proxies = {
    'http': 'http://127.0.0.1:10809',
    'https': 'http://127.0.0.1:10809'
}
# 获取美国随机用户数据
response = requests.get("https://randomuser.me/api/?nat=us", proxies=proxies)
data = response.json()["results"][0]

# # 美国州名缩写映射表
# state_abbr_map = {
#     "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR", "California": "CA",
#     "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE", "Florida": "FL", "Georgia": "GA",
#     "Hawaii": "HI", "Idaho": "ID", "Illinois": "IL", "Indiana": "IN", "Iowa": "IA",
#     "Kansas": "KS", "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
#     "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS", "Missouri": "MO",
#     "Montana": "MT", "Nebraska": "NE", "Nevada": "NV", "New Hampshire": "NH", "New Jersey": "NJ",
#     "New Mexico": "NM", "New York": "NY", "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH",
#     "Oklahoma": "OK", "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
#     "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT", "Vermont": "VT",
#     "Virginia": "VA", "Washington": "WA", "West Virginia": "WV", "Wisconsin": "WI", "Wyoming": "WY"
# }

# # 构建所需格式的用户数据
# formatted_user = {
#     "firstName": data["name"]["first"],
#     "lastName": data["name"]["last"],
#     "addressLine1": f"{data['location']['street']['number']} {data['location']['street']['name']}",
#     "addressLine2": "",  # API没有提供，留空
#     "city": data["location"]["city"],
#     "state": data["location"]["state"],
#     "stateAbbr": state_abbr_map.get(data["location"]["state"], ""),
#     "postalCode": data["location"]["postcode"],
#     "phone": data["phone"]
# }

# print(json.dumps(formatted_user, indent=4))

print(data)
// ==UserScript==
// @name         AugmentCode Token Helper
// @namespace    http://tampermonkey.net/
// @version      2.3
// @description  增强版AugmentCode授权码和Token获取工具，集成临时邮箱和验证码功能
// <AUTHOR> Agent
// @match        https://*.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_registerMenuCommand
// @grant        GM_openInTab
// ==/UserScript==

(function() {
    'use strict';

    // ==================== 样式管理系统 ====================
    const style = document.createElement('style');
    style.textContent = `
        /* 主容器样式 */
        .token-helper-container {
            position: fixed;
            top: 10px;
            left: 10px;
            width: 400px;
            background: rgba(255, 255, 255, 0.98);
            border: 2px solid #007bff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        /* 标题栏样式 */
        .token-helper-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 12px 15px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }

        .token-helper-title {
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            flex: 1;
        }

        .token-helper-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .token-helper-close:hover {
            background-color: rgba(255,255,255,0.2);
        }

        /* 标签页导航样式 */
        .token-helper-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .token-helper-tab {
            flex: 1;
            padding: 10px 8px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.2s;
            border-bottom: 3px solid transparent;
        }

        .token-helper-tab:hover {
            background: #e9ecef;
            color: #495057;
        }

        .token-helper-tab.active {
            color: #007bff;
            background: white;
            border-bottom-color: #007bff;
        }

        /* 内容区域样式 */
        .token-helper-content {
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
        }

        .token-helper-section {
            display: none;
        }

        .token-helper-section.active {
            display: block;
        }

        /* 通用组件样式 */
        .token-helper-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .token-helper-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .token-helper-button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .token-helper-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .token-helper-button:active {
            transform: translateY(0);
        }

        .token-helper-button.secondary {
            background: #6c757d;
        }

        .token-helper-button.secondary:hover {
            background: #545b62;
        }

        .token-helper-button.success {
            background: #28a745;
        }

        .token-helper-button.success:hover {
            background: #1e7e34;
        }

        .token-helper-button.warning {
            background: #ffc107;
            color: #212529;
        }

        .token-helper-button.warning:hover {
            background: #e0a800;
        }

        .token-helper-button.danger {
            background: #dc3545;
        }

        .token-helper-button.danger:hover {
            background: #c82333;
        }

        /* 状态显示样式 */
        .token-helper-status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 12px;
            word-break: break-all;
        }

        .token-helper-status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .token-helper-status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .token-helper-status.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .token-helper-status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        /* 表单组样式 */
        .token-helper-form-group {
            margin-bottom: 15px;
        }

        .token-helper-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .token-helper-button-group {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .token-helper-button-group .token-helper-button {
            flex: 1;
        }

        /* 邮箱相关样式 */
        .token-helper-email-item {
            padding: 8px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .token-helper-email-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }

        .token-helper-email-subject {
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
        }

        .token-helper-email-from {
            font-size: 12px;
            color: #6c757d;
        }

        .token-helper-email-time {
            font-size: 11px;
            color: #adb5bd;
            float: right;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .token-helper-container {
                width: 95%;
                left: 2.5%;
                right: 2.5%;
            }
        }

        /* 滚动条样式 */
        .token-helper-content::-webkit-scrollbar {
            width: 6px;
        }

        .token-helper-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .token-helper-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .token-helper-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    `;
    document.head.appendChild(style);

    // ==================== 控制台日志函数 ====================
    function log(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [TokenHelper] ${message}`, data || '');
    }



    // ==================== 工具函数 ====================

    // 生成随机邮箱
    function generateRandomEmail(domain = 'uuf.me') {
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let randomString = '';
        for (let i = 0; i < 8; i++) {
            randomString += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return randomString + '@' + domain;
    }





    // 检查输入框是否是验证码输入框
    function isCodeInput(input) {
        if (!input) return false;

        const placeholder = (input.getAttribute('placeholder') || '').toLowerCase();
        const name = (input.getAttribute('name') || '').toLowerCase();
        const id = (input.getAttribute('id') || '').toLowerCase();
        const ariaLabel = (input.getAttribute('aria-label') || '').toLowerCase();

        let labelText = '';
        const labelElement = document.querySelector(`label[for="${input.id}"]`);
        if (labelElement) {
            labelText = labelElement.textContent.toLowerCase();
        }

        const codeKeywords = ['code', 'verification', 'verify', 'otp', '验证码', 'enter the code'];

        for (const keyword of codeKeywords) {
            if (
                placeholder.includes(keyword) ||
                name.includes(keyword) ||
                id.includes(keyword) ||
                ariaLabel.includes(keyword) ||
                labelText.includes(keyword) ||
                (input.parentElement && input.parentElement.textContent.toLowerCase().includes(keyword))
            ) {
                log(`输入框被识别为验证码输入框，匹配关键词: ${keyword}`, input);
                return true;
            }
        }

        const maxLength = input.getAttribute('maxlength');
        if (maxLength && parseInt(maxLength) <= 8) {
            log('输入框可能是验证码输入框(基于长度限制):', input);
            return true;
        }

        const inputMode = input.getAttribute('inputmode');
        if (inputMode === 'numeric' || inputMode === 'tel') {
            log('输入框可能是验证码输入框(基于输入模式):', input);
            return true;
        }

        const pattern = input.getAttribute('pattern');
        if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
            log('输入框可能是验证码输入框(基于模式限制):', input);
            return true;
        }

        return false;
    }

    // 查找邮箱输入框
    function findEmailInput() {
        log('开始查找邮箱输入框...');

        const selectors = [
            'input[id="username"]',
            'input[type="email"]',
            'input[name="email"]',
            'input[placeholder*="email" i]',
            'input[id*="email" i]',
            'input[aria-label*="email" i]',
            'input.MuiInputBase-input',
            'input[required][name="email"]',
            'input[name="address"]',
            'input[name="Email address"]',
            'input[placeholder="Email address"]',
            'input[aria-label="Email address"]',
            'form input[type="text"]',
            'form input[type="email"]',
            'form input',
            '.MuiFormControl-root input',
            '.MuiInputBase-root input',
            'input[required][type="text"]',
            'input[required][type="email"]'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input && !isCodeInput(input)) {
                    log(`找到邮箱输入框，使用选择器: ${selector}`);
                    return input;
                } else if (input) {
                    log(`选择器 ${selector} 找到的输入框被识别为验证码输入框，跳过`);
                }
            } catch (e) {
                log(`选择器 ${selector} 出错:`, e);
            }
        }

        const allInputs = document.querySelectorAll('input');
        log(`页面上共有 ${allInputs.length} 个输入框`);

        for (const input of allInputs) {
            if (isCodeInput(input)) {
                continue;
            }

            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const type = input.getAttribute('type') || '';

            if (
                placeholder.toLowerCase().includes('email') ||
                name.toLowerCase().includes('email') ||
                id.toLowerCase().includes('email') ||
                type === 'email' ||
                placeholder.toLowerCase().includes('address') ||
                name.toLowerCase().includes('address') ||
                id.toLowerCase().includes('address')
            ) {
                log('通过属性分析找到可能的邮箱输入框:', input);
                return input;
            }
        }

        const textInputs = Array.from(allInputs).filter(input =>
            (input.type === 'text' || input.type === 'email' || !input.type) && !isCodeInput(input)
        );

        if (textInputs.length > 0) {
            log('未找到明确的邮箱输入框，使用第一个非验证码的文本输入框:', textInputs[0]);
            return textInputs[0];
        }

        log('未找到任何可用的邮箱输入框');
        return null;
    }

    // 查找验证码输入框
    function findCodeInput() {
        log('开始查找验证码输入框...');

        const selectors = [
            'input[placeholder*="code" i]',
            'input[name*="code" i]',
            'input[aria-label*="code" i]',
            'input[id*="code" i]',
            'input.MuiInputBase-input',
            'input[placeholder*="Enter the code" i]',
            'input[placeholder*="verification" i]',
            'input[placeholder*="验证码" i]',
            'form input[type="text"]',
            '.MuiFormControl-root input',
            '.MuiInputBase-root input',
            'form > div > input'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input) {
                    log(`找到验证码输入框，使用选择器: ${selector}`);
                    return input;
                }
            } catch (e) {
                log(`选择器 ${selector} 出错:`, e);
            }
        }

        const allInputs = document.querySelectorAll('input');

        for (const input of allInputs) {
            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';

            if (
                placeholder.toLowerCase().includes('code') ||
                name.toLowerCase().includes('code') ||
                id.toLowerCase().includes('code') ||
                placeholder.toLowerCase().includes('验证码') ||
                name.toLowerCase().includes('验证码') ||
                id.toLowerCase().includes('验证码') ||
                placeholder.toLowerCase().includes('verification') ||
                name.toLowerCase().includes('verification') ||
                id.toLowerCase().includes('verification')
            ) {
                log('通过属性分析找到可能的验证码输入框:', input);
                return input;
            }
        }

        const textInputs = Array.from(allInputs).filter(input => input.type === 'text');

        if (textInputs.length === 1) {
            log('使用唯一的文本输入框作为验证码输入框:', textInputs[0]);
            return textInputs[0];
        } else if (textInputs.length > 1) {
            for (const input of textInputs) {
                const maxLength = input.getAttribute('maxlength');
                if (maxLength && parseInt(maxLength) <= 8) {
                    log('找到可能的验证码输入框(基于长度限制):', input);
                    return input;
                }

                const inputMode = input.getAttribute('inputmode');
                if (inputMode === 'numeric' || inputMode === 'tel') {
                    log('找到可能的验证码输入框(基于输入模式):', input);
                    return input;
                }

                const pattern = input.getAttribute('pattern');
                if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
                    log('找到可能的验证码输入框(基于模式限制):', input);
                    return input;
                }
            }

            if (textInputs.length >= 2) {
                log('使用第二个文本输入框作为验证码输入框:', textInputs[1]);
                return textInputs[1];
            }

            log('使用最后一个文本输入框作为验证码输入框:', textInputs[textInputs.length - 1]);
            return textInputs[textInputs.length - 1];
        }

        log('未找到任何可用的验证码输入框');
        return null;
    }

    // ==================== OAuth相关功能 ====================

    // Code Verifier展开/收起切换函数
    function toggleVerifier(element, fullVerifier) {
        const isExpanded = element.textContent === fullVerifier;
        if (isExpanded) {
            const shortVerifier = `${fullVerifier.substring(0, 8)}...${fullVerifier.substring(fullVerifier.length - 8)}`;
            element.textContent = shortVerifier;
            element.title = '点击展开完整Code Verifier';
        } else {
            element.textContent = fullVerifier;
            element.title = '点击收起Code Verifier';
        }
    }

    // 将函数暴露到全局作用域
    window.toggleVerifier = toggleVerifier;

    // PKCE相关函数
    function base64URLEncode(buffer) {
        return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    async function sha256Hash(input) {
        const encoder = new TextEncoder();
        const data = encoder.encode(input);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return hashBuffer;
    }

    async function createOAuthState() {
        log('开始创建OAuth状态...');

        const codeVerifierArray = new Uint8Array(32);
        crypto.getRandomValues(codeVerifierArray);
        const codeVerifier = base64URLEncode(codeVerifierArray.buffer);

        const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));

        const stateArray = new Uint8Array(8);
        crypto.getRandomValues(stateArray);
        const state = base64URLEncode(stateArray.buffer);

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: Date.now()
        };

        log('OAuth状态创建完成', oauthState);
        GM_setValue('oauthState', JSON.stringify(oauthState));
        return oauthState;
    }

    // 解析授权码
    function parseCode(code) {
        log('开始解析授权码...', { inputCode: code });

        try {
            const parsed = JSON.parse(code);
            const result = {
                code: parsed.code,
                state: parsed.state,
                tenant_url: parsed.tenant_url,
            };

            log('授权码解析成功', result);
            return result;
        } catch (error) {
            log('授权码解析失败', { error: error.message, inputCode: code });
            throw new Error(`授权码解析失败: ${error.message}`);
        }
    }

    // 获取token函数
    async function getAccessToken(tenant_url, codeVerifier, code) {
        log('开始获取访问令牌...', { tenant_url, codeVerifier, code });

        const clientID = "v";
        const data = {
            grant_type: "authorization_code",
            client_id: clientID,
            code_verifier: codeVerifier,
            redirect_uri: "",
            code: code
        };

        log('准备发送token请求', { endpoint: `${tenant_url}token`, data });

        try {
            const result = await new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: "POST",
                    url: `${tenant_url}token`,
                    data: JSON.stringify(data),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    onload: function(response) {
                        log('收到token响应', {
                            status: response.status,
                            responseText: response.responseText
                        });

                        if (response.status === 200) {
                            try {
                                const json = JSON.parse(response.responseText);
                                if (json.access_token) {
                                    log('Token获取成功', { token: json.access_token });
                                    resolve(json.access_token);
                                } else {
                                    log('响应中没有access_token', json);
                                    reject(new Error(`No access_token in response: ${response.responseText}`));
                                }
                            } catch (error) {
                                log('JSON解析错误', { error: error.message });
                                reject(new Error(`JSON parse error: ${error.message}`));
                            }
                        } else {
                            log('HTTP请求失败', { status: response.status, responseText: response.responseText });
                            reject(new Error(`HTTP ${response.status}: ${response.responseText}`));
                        }
                    },
                    onerror: function(error) {
                        log('网络请求错误', error);
                        reject(new Error(`Network error: ${error}`));
                    }
                });
            });

            return result;
        } catch (error) {
            log('Token获取失败', { error: error.message });
            throw error;
        }
    }

    // 生成授权链接
    async function generateAuthorizationUrl() {
        log('开始生成授权链接...');

        try {
            const oauthState = await createOAuthState();
            const baseUrl = 'https://auth.augmentcode.com/authorize';
            const params = new URLSearchParams({
                client_id: 'v',
                response_type: 'code',
                redirect_uri: '',
                code_challenge: oauthState.codeChallenge,
                code_challenge_method: 'S256',
                state: oauthState.state
            });

            const authUrl = `${baseUrl}?${params.toString()}`;
            log('授权链接生成完成', { authUrl });
            return authUrl;
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            throw error;
        }
    }

    // 处理菜单命令：生成授权链接
    async function handleGenerateAuthLink() {
        try {
            const authUrl = await generateAuthorizationUrl();
            GM_openInTab(authUrl, true);
            log('授权链接已在新标签页打开');
        } catch (error) {
            log('生成授权链接失败', { error: error.message });
            alert(`生成授权链接失败: ${error.message}`);
        }
    }

    // ==================== 数据存储管理 ====================

    // 保存和获取数据
    function saveData(key, value) {
        GM_setValue(key, value);
    }

    function getData(key, defaultValue = null) {
        return GM_getValue(key, defaultValue);
    }

    // 清除所有保存的数据
    function clearAllData() {
        saveData('currentEmail', '');
        saveData('verificationCode', '');
        log('已清除所有保存的数据');
    }

    // ==================== UI创建函数 ====================

    // 创建主UI容器
    function createMainUI() {
        log('开始创建主UI容器...');

        const container = document.createElement('div');
        container.className = 'token-helper-container';
        container.id = 'token-helper-container';

        // 创建标题栏
        const header = document.createElement('div');
        header.className = 'token-helper-header';
        header.innerHTML = `
            <div class="token-helper-title">AugmentCode Token Helper</div>
            <button class="token-helper-close" id="token-helper-close">×</button>
        `;
        container.appendChild(header);

        // 创建标签页导航
        const tabs = document.createElement('div');
        tabs.className = 'token-helper-tabs';
        tabs.innerHTML = `
            <button class="token-helper-tab active" data-tab="register">注册助手</button>
            <button class="token-helper-tab" data-tab="token">Token获取</button>
        `;
        container.appendChild(tabs);

        // 创建内容区域
        const content = document.createElement('div');
        content.className = 'token-helper-content';
        container.appendChild(content);

        // 创建注册助手标签页（合并邮箱和验证码功能）- 默认显示
        const registerSection = createRegisterSection();
        content.appendChild(registerSection);

        // 创建Token获取标签页
        const tokenSection = createTokenSection();
        content.appendChild(tokenSection);

        // 设置事件监听器
        setupEventListeners(container);

        log('主UI容器创建完成');
        return container;
    }

    // 创建Token获取标签页
    function createTokenSection() {
        const section = document.createElement('div');
        section.className = 'token-helper-section';
        section.id = 'token-section';

        // 显示当前OAuth状态
        const statusDiv = document.createElement('div');
        statusDiv.className = 'token-helper-status info';
        statusDiv.id = 'oauth-status';

        const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
        if (savedOAuthState.codeVerifier) {
            const shortVerifier = `${savedOAuthState.codeVerifier.substring(0, 8)}...${savedOAuthState.codeVerifier.substring(savedOAuthState.codeVerifier.length - 8)}`;
            statusDiv.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#28a745;">已准备</span><br>
                <strong>State:</strong> ${savedOAuthState.state}<br>
                <strong>Code Verifier:</strong> <span onclick="toggleVerifier(this, '${savedOAuthState.codeVerifier}')" style="cursor:pointer; color:#007bff; text-decoration:underline;" title="点击展开完整Code Verifier">${shortVerifier}</span><br>
                <strong>创建时间:</strong> ${new Date(savedOAuthState.creationTime).toLocaleString()}<br>
                <button onclick="GM_deleteValue('oauthState');location.reload();" class="token-helper-button danger" style="margin-top:8px; font-size:12px; padding:4px 8px;">清除状态</button>
            `;
        } else {
            statusDiv.innerHTML = `
                <strong>OAuth状态:</strong> <span style="color:#dc3545;">未准备</span><br>
                <small>请先通过菜单"生成授权链接"创建OAuth状态</small>
            `;
        }
        section.appendChild(statusDiv);

        // 自动检测区域
        const autoDiv = document.createElement('div');
        autoDiv.className = 'token-helper-status success';
        autoDiv.innerHTML = `
            <h4 style="margin:0 0 10px 0; color:#155724;">自动检测</h4>
            <div id="auto-detection-result" style="margin:10px 0; word-break:break-all; font-size:12px;">
                正在检测授权码...
            </div>
            <div class="token-helper-button-group">
                <button id="redetect-btn" class="token-helper-button secondary">重新检测</button>
                <button id="clipboard-btn" class="token-helper-button warning">从剪贴板获取</button>
            </div>
        `;
        section.appendChild(autoDiv);

        // Token结果显示区域
        const resultDiv = document.createElement('div');
        resultDiv.id = 'token-result-area';
        section.appendChild(resultDiv);

        return section;
    }

    // 创建注册助手标签页（合并邮箱和验证码功能）
    function createRegisterSection() {
        const section = document.createElement('div');
        section.className = 'token-helper-section active';
        section.id = 'register-section';

        section.innerHTML = `
            <!-- 邮箱管理区域 -->
            <div class="token-helper-form-group">
                <label class="token-helper-label">📧 临时邮箱</label>
                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                    <select id="domain-select" class="token-helper-input" style="flex: 1;">
                        <option value="uuf.me">@uuf.me</option>
                        <option value="qabq.com">@qabq.com</option>
                        <option value="nqmo.com">@nqmo.com</option>
                        <option value="end.tw">@end.tw</option>
                    </select>
                    <button id="generate-email-btn" class="token-helper-button" style="min-width: 80px;">生成邮箱</button>
                </div>
                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                    <input type="text" id="email-input" class="token-helper-input" placeholder="点击生成临时邮箱" style="flex: 1;">
                    <button id="copy-email-btn" class="token-helper-button secondary" style="min-width: 60px;">复制</button>
                </div>
                <div style="text-align: center;">
                    <button id="fill-email-btn" class="token-helper-button success" style="width: 100%;">📝 填充到页面</button>
                </div>
            </div>

            <!-- 验证码管理区域 -->
            <div class="token-helper-form-group">
                <label class="token-helper-label">🔐 验证码</label>
                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                    <input type="text" id="code-input" class="token-helper-input" placeholder="请手动输入验证码" style="flex: 1;">
                    <button id="copy-code-btn" class="token-helper-button secondary" style="min-width: 60px;">复制</button>
                </div>
                <div class="token-helper-button-group">
                    <button id="fill-code-btn" class="token-helper-button success">📝 填充验证码</button>
                    <button id="clear-data-btn" class="token-helper-button danger">🗑️ 清除数据</button>
                </div>
            </div>

            <!-- 状态显示区域 -->
            <div class="token-helper-status info" id="register-status">
                等待操作...
            </div>

            <!-- 使用说明 -->
            <div class="token-helper-status warning" style="font-size: 12px; margin-top: 10px;">
                <strong>📋 使用流程：</strong><br>
                1️⃣ 选择域名并点击"生成邮箱"创建临时邮箱<br>
                2️⃣ 点击"填充到页面"将邮箱自动填入注册表单<br>
                3️⃣ 在注册页面提交邮箱后，手动查看邮件获取验证码<br>
                4️⃣ 在验证码输入框中输入验证码，然后点击"填充验证码"
            </div>
        `;

        return section;
    }

    // 设置事件监听器
    function setupEventListeners(container) {
        log('开始设置事件监听器...');

        // 关闭按钮
        const closeBtn = container.querySelector('#token-helper-close');
        closeBtn.addEventListener('click', () => {
            container.style.display = 'none';
        });

        // 标签页切换
        const tabs = container.querySelectorAll('.token-helper-tab');
        const sections = container.querySelectorAll('.token-helper-section');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;

                // 更新标签页状态
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // 更新内容区域
                sections.forEach(s => s.classList.remove('active'));
                const targetSection = container.querySelector(`#${targetTab}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                log(`切换到标签页: ${targetTab}`);
            });
        });

        // 拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        const header = container.querySelector('.token-helper-header');
        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragOffset.x = e.clientX - container.offsetLeft;
            dragOffset.y = e.clientY - container.offsetTop;
            header.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                container.style.left = (e.clientX - dragOffset.x) + 'px';
                container.style.top = (e.clientY - dragOffset.y) + 'px';
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                header.style.cursor = 'move';
            }
        });

        // 设置Token相关事件
        setupTokenEvents(container);

        // 设置注册助手相关事件（合并邮箱和验证码功能）
        setupRegisterEvents(container);

        log('事件监听器设置完成');
    }

    // ==================== 事件处理函数 ====================

    // 设置Token相关事件
    function setupTokenEvents(container) {
        // 重新检测按钮
        const redetectBtn = container.querySelector('#redetect-btn');
        redetectBtn.addEventListener('click', () => {
            performAutoDetection(container);
        });

        // 从剪贴板获取按钮
        const clipboardBtn = container.querySelector('#clipboard-btn');
        clipboardBtn.addEventListener('click', async () => {
            try {
                const clipboardText = await navigator.clipboard.readText();
                log('从剪贴板读取内容', { clipboardText });

                const parsed = JSON.parse(clipboardText);
                if (parsed.code && parsed.state && parsed.tenant_url) {
                    log('剪贴板中找到有效授权码', parsed);

                    const resultDiv = container.querySelector('#auto-detection-result');
                    resultDiv.innerHTML = `
                        <strong>✅ 从剪贴板检测到授权码:</strong><br>
                        <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                        <strong>State:</strong> ${parsed.state}<br>
                        <strong>Tenant URL:</strong> ${parsed.tenant_url}
                    `;

                    // 自动触发Token获取
                    setTimeout(() => {
                        autoTriggerTokenGet(clipboardText, container.querySelector('#token-result-area'), '剪贴板自动检测');
                    }, 1000);

                } else {
                    const resultDiv = container.querySelector('#auto-detection-result');
                    resultDiv.innerHTML = '<span style="color:red">剪贴板内容不是有效的授权码JSON</span>';
                }
            } catch (error) {
                log('从剪贴板获取失败', { error: error.message });
                const resultDiv = container.querySelector('#auto-detection-result');
                resultDiv.innerHTML = `<span style="color:red">从剪贴板获取失败: ${error.message}</span>`;
            }
        });

        // 执行初始自动检测
        performAutoDetection(container);
    }

    // 设置注册助手相关事件（合并邮箱和验证码功能）
    function setupRegisterEvents(container) {
        let currentEmail = getData('currentEmail', '');

        // 如果有保存的邮箱和验证码，显示在输入框中
        if (currentEmail) {
            const emailInput = container.querySelector('#email-input');
            emailInput.value = currentEmail;
        }

        const savedCode = getData('verificationCode', '');
        if (savedCode) {
            const codeInput = container.querySelector('#code-input');
            const statusDiv = container.querySelector('#register-status');

            codeInput.value = savedCode;
            statusDiv.className = 'token-helper-status success';
            statusDiv.innerHTML = `已恢复数据 - 邮箱: <strong>${currentEmail}</strong> | 验证码: <strong>${savedCode}</strong>`;
        }

        // 生成邮箱按钮
        const generateBtn = container.querySelector('#generate-email-btn');
        generateBtn.addEventListener('click', () => {
            const domainSelect = container.querySelector('#domain-select');
            const emailInput = container.querySelector('#email-input');
            const statusDiv = container.querySelector('#register-status');

            const domain = domainSelect.value;
            currentEmail = generateRandomEmail(domain);

            emailInput.value = currentEmail;
            saveData('currentEmail', currentEmail);

            statusDiv.className = 'token-helper-status success';
            statusDiv.innerHTML = `✅ 邮箱已生成: <strong>${currentEmail}</strong>`;

            log('邮箱生成成功:', currentEmail);
        });

        // 复制邮箱按钮
        const copyEmailBtn = container.querySelector('#copy-email-btn');
        copyEmailBtn.addEventListener('click', async () => {
            const emailInput = container.querySelector('#email-input');
            const email = emailInput.value;

            if (email) {
                try {
                    await navigator.clipboard.writeText(email);
                    const statusDiv = container.querySelector('#register-status');
                    statusDiv.className = 'token-helper-status success';
                    statusDiv.innerHTML = '📋 邮箱地址已复制到剪贴板';

                    setTimeout(() => {
                        statusDiv.className = 'token-helper-status info';
                        statusDiv.innerHTML = '等待操作...';
                    }, 2000);
                } catch (error) {
                    log('复制邮箱失败:', error);
                }
            }
        });

        // 填充邮箱按钮
        const fillEmailBtn = container.querySelector('#fill-email-btn');
        fillEmailBtn.addEventListener('click', () => {
            const emailInput = container.querySelector('#email-input');
            const email = emailInput.value;
            const statusDiv = container.querySelector('#register-status');

            if (!email) {
                statusDiv.className = 'token-helper-status error';
                statusDiv.innerHTML = '❌ 请先生成或输入邮箱地址';
                return;
            }

            const emailInputField = findEmailInput();
            if (emailInputField) {
                try {
                    emailInputField.value = email;
                    emailInputField.dispatchEvent(new Event('input', { bubbles: true }));
                    emailInputField.dispatchEvent(new Event('change', { bubbles: true }));

                    statusDiv.className = 'token-helper-status success';
                    statusDiv.innerHTML = '✅ 邮箱已填充到页面输入框';

                    log('邮箱填充成功:', email);
                } catch (error) {
                    statusDiv.className = 'token-helper-status error';
                    statusDiv.innerHTML = '❌ 邮箱填充失败，请手动复制';
                    log('邮箱填充失败:', error);
                }
            } else {
                statusDiv.className = 'token-helper-status warning';
                statusDiv.innerHTML = '⚠️ 未找到邮箱输入框，请手动复制邮箱';
            }
        });

        // ==================== 验证码相关事件 ====================

        // 复制验证码按钮
        const copyCodeBtn = container.querySelector('#copy-code-btn');
        copyCodeBtn.addEventListener('click', async () => {
            const codeInput = container.querySelector('#code-input');
            const code = codeInput.value;

            if (code) {
                try {
                    await navigator.clipboard.writeText(code);
                    const statusDiv = container.querySelector('#register-status');
                    statusDiv.className = 'token-helper-status success';
                    statusDiv.innerHTML = '📋 验证码已复制到剪贴板';

                    setTimeout(() => {
                        statusDiv.className = 'token-helper-status info';
                        statusDiv.innerHTML = '等待操作...';
                    }, 2000);
                } catch (error) {
                    log('复制验证码失败:', error);
                }
            }
        });

        // 填充验证码按钮
        const fillCodeBtn = container.querySelector('#fill-code-btn');
        fillCodeBtn.addEventListener('click', () => {
            const codeInput = container.querySelector('#code-input');
            const code = codeInput.value;
            const statusDiv = container.querySelector('#register-status');

            if (!code) {
                statusDiv.className = 'token-helper-status error';
                statusDiv.innerHTML = '❌ 请先获取验证码';
                return;
            }

            const codeInputField = findCodeInput();
            if (codeInputField) {
                try {
                    codeInputField.value = code;
                    codeInputField.dispatchEvent(new Event('input', { bubbles: true }));
                    codeInputField.dispatchEvent(new Event('change', { bubbles: true }));

                    statusDiv.className = 'token-helper-status success';
                    statusDiv.innerHTML = '✅ 验证码已填充到页面输入框';

                    log('验证码填充成功:', code);

                    // 成功填充验证码后，延迟清除数据
                    setTimeout(() => {
                        clearAllData();
                        const emailInput = container.querySelector('#email-input');
                        emailInput.value = '';
                        codeInput.value = '';
                        statusDiv.className = 'token-helper-status info';
                        statusDiv.innerHTML = '🗑️ 数据已自动清除';
                    }, 3000);

                } catch (error) {
                    statusDiv.className = 'token-helper-status error';
                    statusDiv.innerHTML = '❌ 验证码填充失败，请手动复制';
                    log('验证码填充失败:', error);
                }
            } else {
                statusDiv.className = 'token-helper-status warning';
                statusDiv.innerHTML = '⚠️ 未找到验证码输入框，请手动复制验证码';
            }
        });

        // 清除数据按钮
        const clearDataBtn = container.querySelector('#clear-data-btn');
        clearDataBtn.addEventListener('click', () => {
            clearAllData();
            const emailInput = container.querySelector('#email-input');
            const codeInput = container.querySelector('#code-input');
            const statusDiv = container.querySelector('#register-status');

            emailInput.value = '';
            codeInput.value = '';
            statusDiv.className = 'token-helper-status info';
            statusDiv.innerHTML = '🗑️ 所有数据已清除';

            log('用户手动清除数据');
        });
    }

    // ==================== 核心功能函数 ====================

    // 执行自动检测
    function performAutoDetection(container) {
        const resultDiv = container.querySelector('#auto-detection-result');
        resultDiv.innerHTML = '正在检测授权码...';

        const authCode = autoExtractCode();
        if (authCode) {
            try {
                const parsed = parseCode(authCode);
                resultDiv.innerHTML = `
                    <strong>✅ 检测到授权码:</strong><br>
                    <strong>Code:</strong> <span style="font-family:monospace; font-size:10px;">${parsed.code}</span><br>
                    <strong>State:</strong> ${parsed.state}<br>
                    <strong>Tenant URL:</strong> ${parsed.tenant_url}
                `;

                // 自动触发Token获取
                log('检测到授权码，自动触发Token获取流程');
                setTimeout(() => {
                    autoTriggerTokenGet(authCode, container.querySelector('#token-result-area'), '页面加载自动检测');
                }, 1000);

            } catch (error) {
                resultDiv.innerHTML = `<span style="color:red">授权码解析失败: ${error.message}</span>`;
            }
        } else {
            resultDiv.innerHTML = '<span style="color:#856404;">未检测到授权码</span>';
        }
    }

    // 专注于Script标签的授权码提取
    function autoExtractCode() {
        log('=== 开始Script标签授权码提取 ===');

        const scripts = document.querySelectorAll('script');
        log('找到script标签数量:', scripts.length);

        for (let i = 0; i < scripts.length; i++) {
            const script = scripts[i];
            if (script.textContent) {
                const content = script.textContent;
                log(`检查script ${i}...`);

                // 方法1: 查找 data = {...} 模式，支持多行
                const dataMatch = content.match(/data\s*=\s*\{[^}]*code[^}]*\}/s);
                if (dataMatch) {
                    log(`在script ${i}中找到data变量:`, dataMatch[0]);

                    const dataStr = dataMatch[0].replace(/data\s*=\s*/, '');
                    log('提取的data内容:', dataStr);

                    try {
                        let jsonStr = dataStr
                            .replace(/\s+/g, ' ')
                            .replace(/(\w+):/g, '"$1":')
                            .replace(/,\s*}/g, '}')
                            .trim();

                        log('转换后的JSON:', jsonStr);

                        const parsed = JSON.parse(jsonStr);
                        log('JSON验证成功:', parsed);

                        if (parsed.code && parsed.state && parsed.tenant_url) {
                            const compactJson = JSON.stringify(parsed);
                            log('紧凑JSON格式:', compactJson);
                            return compactJson;
                        } else {
                            log('缺少必需字段:', { code: !!parsed.code, state: !!parsed.state, tenant_url: !!parsed.tenant_url });
                        }
                    } catch (e) {
                        log('JSON转换失败，尝试手动提取:', e.message);

                        const codeMatch = dataStr.match(/code:\s*"([^"]+)"/);
                        const stateMatch = dataStr.match(/state:\s*"([^"]+)"/);
                        const tenantMatch = dataStr.match(/tenant_url:\s*"([^"]+)"/);

                        if (codeMatch && stateMatch && tenantMatch) {
                            const manualJson = JSON.stringify({
                                code: codeMatch[1],
                                state: stateMatch[1],
                                tenant_url: tenantMatch[1]
                            });
                            log('手动构造的JSON:', manualJson);
                            return manualJson;
                        }
                    }
                }

                // 方法2: 直接搜索标准JSON格式
                const jsonMatches = content.match(/\{"code":"[^"]+","state":"[^"]+","tenant_url":"[^"]+"\}/g);
                if (jsonMatches && jsonMatches.length > 0) {
                    const jsonText = jsonMatches[0];
                    log(`在script ${i}中找到标准JSON:`, jsonText);

                    try {
                        const parsed = JSON.parse(jsonText);
                        if (parsed.code && parsed.state && parsed.tenant_url) {
                            log('标准JSON验证成功:', parsed);
                            return jsonText;
                        }
                    } catch (e) {
                        log('标准JSON解析失败:', e.message);
                    }
                }

                // 方法3: 如果包含关键字段，尝试分别提取
                if (content.includes('code') && content.includes('tenant_url')) {
                    log(`检查script ${i}是否包含分散的字段...`);

                    const codeMatch = content.match(/"code"\s*:\s*"([^"]+)"/);
                    if (codeMatch) {
                        const code = codeMatch[1];
                        log('找到code值:', code);

                        const stateMatch = content.match(/"state"\s*:\s*"([^"]+)"/);
                        const state = stateMatch ? stateMatch[1] : 'default_state';
                        log('找到state值:', state);

                        const tenantMatch = content.match(/"tenant_url"\s*:\s*"([^"]+)"/);
                        const tenant_url = tenantMatch ? tenantMatch[1] : window.location.origin + '/';
                        log('找到tenant_url值:', tenant_url);

                        const result = {
                            code: code,
                            state: state,
                            tenant_url: tenant_url
                        };

                        const resultJson = JSON.stringify(result);
                        log('构造的授权码JSON:', resultJson);
                        return resultJson;
                    }
                }
            }
        }

        log('未找到授权码');
        return null;
    }

    // 自动获取Token流程
    async function autoGetToken(authCode) {
        log('开始自动Token获取流程...', { authCode });

        try {
            const parsedCode = parseCode(authCode);

            const savedOAuthState = JSON.parse(GM_getValue('oauthState', '{}'));
            if (!savedOAuthState.codeVerifier) {
                throw new Error('未找到保存的Code Verifier，请先生成授权链接');
            }

            log('找到保存的OAuth状态', savedOAuthState);
            log('跳过State验证（自动检测模式）');

            const token = await getAccessToken(
                parsedCode.tenant_url,
                savedOAuthState.codeVerifier,
                parsedCode.code
            );

            log('Token获取流程完成', { token });

            GM_deleteValue('oauthState');
            log('已清除保存的OAuth状态');

            return {
                token,
                parsedCode,
                success: true
            };

        } catch (error) {
            log('Token获取流程失败', { error: error.message });
            return {
                error: error.message,
                success: false
            };
        }
    }

    // 统一的自动Token获取函数
    async function autoTriggerTokenGet(authCode, resultContainer, sourceType = '自动检测') {
        log(`开始${sourceType}的自动Token获取流程...`);

        const tokenResult = document.createElement('div');
        tokenResult.style.margin = '10px 0';
        tokenResult.style.wordBreak = 'break-all';
        resultContainer.appendChild(tokenResult);

        tokenResult.innerHTML = `
            <div class="token-helper-status info">
                <strong>🔄 自动获取Token中...</strong><br>
                <small>正在使用检测到的授权码自动获取访问令牌</small>
            </div>
        `;

        try {
            const result = await autoGetToken(authCode);

            if (result.success) {
                tokenResult.innerHTML = `
                    <div class="token-helper-status success">
                        <strong>✅ Token获取成功!</strong><br>
                        <textarea readonly style="width:100%;height:80px;font-size:10px; margin:5px 0; padding:8px; border:1px solid #c3e6cb; border-radius:4px;">${result.token}</textarea><br>
                        <div class="token-helper-button-group">
                            <button onclick="navigator.clipboard.writeText('${result.token}');alert('已复制Token!')" class="token-helper-button success">复制Token</button>
                            <button onclick="this.parentElement.parentElement.parentElement.style.display='none'" class="token-helper-button secondary">收起</button>
                        </div>
                    </div>
                `;
                log(`${sourceType}自动Token获取成功`);
            } else {
                tokenResult.innerHTML = `
                    <div class="token-helper-status error">
                        <strong>❌ Token获取失败:</strong><br>
                        <span style="color:red">${result.error}</span><br>
                        <button onclick="autoTriggerTokenGet('${authCode}', this.parentElement.parentElement.parentElement, '${sourceType}重试')" class="token-helper-button danger" style="margin-top:8px;">重试获取</button>
                    </div>
                `;
                log(`${sourceType}自动Token获取失败:`, result.error);
            }
        } catch (error) {
            tokenResult.innerHTML = `
                <div class="token-helper-status error">
                    <strong>❌ Token获取异常:</strong><br>
                    <span style="color:red">${error.message}</span><br>
                    <button onclick="autoTriggerTokenGet('${authCode}', this.parentElement.parentElement.parentElement, '${sourceType}重试')" class="token-helper-button danger" style="margin-top:8px;">重试获取</button>
                </div>
            `;
            log(`${sourceType}自动Token获取异常:`, error);
        }
    }

    // 将函数暴露到全局作用域
    window.autoTriggerTokenGet = autoTriggerTokenGet;



    // ==================== 初始化和启动 ====================

    // 注册菜单命令
    GM_registerMenuCommand('生成授权链接', handleGenerateAuthLink);

    // 页面加载完成后创建UI
    window.addEventListener('load', () => {
        log('页面加载完成，开始初始化增强版Token Helper');

        // 延迟创建UI，确保页面完全加载
        setTimeout(() => {
            const ui = createMainUI();
            document.body.appendChild(ui);
            log('增强版AugmentCode Token Helper已启动');
        }, 1000);
    });

})();

// ==UserScript==
// @name         美国个人信息生成器(纯API版)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  使用RandomUser.me API生成美国个人信息，每条信息可单独复制
// <AUTHOR>
// @match        *://*/*
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @connect      randomuser.me
// ==/UserScript==

(function() {
    'use strict';

    // 添加样式
    GM_addStyle(`
        #faker-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 10000;
            font-family: Arial, sans-serif;
        }
        
        #faker-generate-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        
        #faker-generate-btn:hover {
            background-color: #45a049;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        #faker-generate-btn.loading {
            background-color: #888;
            cursor: wait;
        }
        
        #faker-result-container {
            position: fixed;
            bottom: 70px;
            right: 20px;
            width: 380px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 15px;
            display: none;
            z-index: 10000;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        #faker-result-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .faker-close-btn {
            background: none;
            border: none;
            color: #999;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }
        
        .faker-close-btn:hover {
            color: #666;
        }
        
        .faker-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        
        .faker-info-label {
            font-weight: bold;
            color: #555;
            width: 70px;
        }
        
        .faker-info-value {
            flex-grow: 1;
            margin: 0 10px;
            word-break: break-all;
        }
        
        .faker-copy-item-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            min-width: 50px;
        }
        
        .faker-copy-item-btn:hover {
            background-color: #0b7dda;
        }
        
        .faker-copy-all-btn {
            background-color: #673AB7;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }
        
        .faker-copy-all-btn:hover {
            background-color: #5E35B1;
        }
        
        .faker-success-message {
            position: fixed;
            bottom: 120px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .faker-loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: faker-spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes faker-spin {
            to { transform: rotate(360deg); }
        }
        
        .faker-status-message {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            text-align: center;
        }
        
        .faker-status-message.error {
            background-color: #ffebee;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
        }
        
        .faker-status-message.info {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .faker-status-message.success {
            background-color: #e8f5e9;
            color: #388e3c;
            border: 1px solid #c8e6c9;
        }
    `);

    // 创建UI元素
    const panel = document.createElement('div');
    panel.id = 'faker-panel';
    panel.innerHTML = `
        <button id="faker-generate-btn">生成美国个人信息</button>
        <div id="faker-result-container">
            <h3>
                生成的美国个人信息
                <button class="faker-close-btn">×</button>
            </h3>
            <div id="faker-result-items"></div>
            <button class="faker-copy-all-btn">复制所有信息</button>
            <div id="faker-status-container"></div>
        </div>
        <div class="faker-success-message">已复制到剪贴板</div>
    `;
    
    document.body.appendChild(panel);
    
    // 获取元素引用
    const generateBtn = document.getElementById('faker-generate-btn');
    const resultContainer = document.getElementById('faker-result-container');
    const resultItems = document.getElementById('faker-result-items');
    const copyAllBtn = document.querySelector('.faker-copy-all-btn');
    const closeBtn = document.querySelector('.faker-close-btn');
    const successMessage = document.querySelector('.faker-success-message');
    const statusContainer = document.getElementById('faker-status-container');
    
    // 显示状态消息
    function showStatusMessage(message, type = 'info') {
        const statusDiv = document.createElement('div');
        statusDiv.className = `faker-status-message ${type}`;
        statusDiv.textContent = message;
        
        // 清除之前的状态消息
        statusContainer.innerHTML = '';
        statusContainer.appendChild(statusDiv);
        
        // 如果是成功消息，3秒后自动清除
        if (type === 'success') {
            setTimeout(() => {
                statusDiv.remove();
            }, 3000);
        }
    }
    
    // 设置按钮加载状态
    function setButtonLoading(isLoading) {
        if (isLoading) {
            generateBtn.innerHTML = '<span class="faker-loading-spinner"></span>加载中...';
            generateBtn.classList.add('loading');
            generateBtn.disabled = true;
        } else {
            generateBtn.innerHTML = '生成美国个人信息';
            generateBtn.classList.remove('loading');
            generateBtn.disabled = false;
        }
    }
    
    // 使用RandomUser.me API生成数据
    function generateFromAPI() {
        return new Promise((resolve, reject) => {
            showStatusMessage('正在从RandomUser.me API获取数据...', 'info');
            
            GM_xmlhttpRequest({
                method: 'GET',
                url: 'https://randomuser.me/api/?nat=us&inc=name,login,email,location,phone,id,picture',
                timeout: 10000,
                onload: function(response) {
                    try {
                        if (response.status !== 200) {
                            reject(new Error(`API响应错误: ${response.status}`));
                            return;
                        }
                        
                        const data = JSON.parse(response.responseText).results[0];
                        
                        // 生成密码和信用卡信息（API不提供）
                        const password = generatePassword(12, true);
                        const creditCardInfo = generateCreditCardInfo();
                        const ssn = data.id.value || generateSSN();
                        
                        const result = {
                            fullName: `${data.name.first} ${data.name.last}`,
                            username: data.login.username,
                            password: password,
                            email: data.email,
                            streetAddress: `${data.location.street.number} ${data.location.street.name}`,
                            city: data.location.city,
                            state: `${data.location.state} (${data.location.state.substring(0, 2).toUpperCase()})`,
                            zipCode: data.location.postcode.toString(),
                            phoneNumber: data.phone,
                            ssn: ssn,
                            creditCardNumber: creditCardInfo.number,
                            creditCardExpiry: creditCardInfo.expiry,
                            creditCardCVV: creditCardInfo.cvv,
                            picture: data.picture.large
                        };
                        
                        showStatusMessage('API数据获取成功', 'success');
                        resolve(result);
                    } catch (error) {
                        console.error('解析API数据失败:', error);
                        reject(error);
                    }
                },
                onerror: function(error) {
                    console.error('API请求失败:', error);
                    reject(new Error('API请求失败'));
                },
                ontimeout: function() {
                    console.error('API请求超时');
                    reject(new Error('API请求超时'));
                }
            });
        });
    }
    
    // 生成随机密码
    function generatePassword(length = 12, includeSpecial = true) {
        const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        const specialChars = "!@#$%^&*()_+-=";
        const allChars = includeSpecial ? chars + specialChars : chars;
        
        let password = '';
        for (let i = 0; i < length; i++) {
            password += allChars.charAt(Math.floor(Math.random() * allChars.length));
        }
        return password;
    }
    
    // 生成随机SSN
    function generateSSN() {
        const part1 = Math.floor(Math.random() * 900 + 100);
        const part2 = Math.floor(Math.random() * 90 + 10);
        const part3 = Math.floor(Math.random() * 9000 + 1000);
        return `${part1}-${part2}-${part3}`;
    }
    
    // 生成信用卡信息
    function generateCreditCardInfo() {
        // 生成16位信用卡号
        let number = '';
        for (let i = 0; i < 4; i++) {
            number += Math.floor(Math.random() * 9000 + 1000);
            if (i < 3) number += ' ';
        }
        
        // 生成有效期
        const currentYear = new Date().getFullYear();
        const month = (Math.floor(Math.random() * 12) + 1).toString().padStart(2, '0');
        const year = (currentYear + Math.floor(Math.random() * 5) + 1).toString().slice(-2);
        const expiry = `${month}/${year}`;
        
        // 生成CVV
        const cvv = Math.floor(Math.random() * 900 + 100).toString();
        
        return { number, expiry, cvv };
    }
    
    // 显示生成的信息
    function displayInfo(info) {
        // 清空之前的结果
        resultItems.innerHTML = '';
        
        // 定义要显示的字段和标签
        const fields = [
            { key: 'fullName', label: '姓名' },
            { key: 'username', label: '用户名' },
            { key: 'password', label: '密码' },
            { key: 'email', label: '邮箱' },
            { key: 'streetAddress', label: '地址' },
            { key: 'city', label: '城市' },
            { key: 'state', label: '州/省' },
            { key: 'zipCode', label: '邮编' },
            { key: 'phoneNumber', label: '电话' },
            { key: 'ssn', label: 'SSN' },
            { key: 'creditCardNumber', label: '信用卡' },
            { key: 'creditCardExpiry', label: '有效期' },
            { key: 'creditCardCVV', label: 'CVV' }
        ];
        
        // 如果有头像，显示头像
        if (info.picture) {
            const pictureDiv = document.createElement('div');
            pictureDiv.style.textAlign = 'center';
            pictureDiv.style.marginBottom = '15px';
            pictureDiv.innerHTML = `<img src="${info.picture}" alt="Profile Picture" style="width: 100px; height: 100px; border-radius: 50%; border: 2px solid #eee;">`;
            resultItems.appendChild(pictureDiv);
        }
        
        // 为每个字段创建一个带复制按钮的项
        fields.forEach(field => {
            const value = info[field.key];
            if (value) {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'faker-info-item';
                itemDiv.innerHTML = `
                    <span class="faker-info-label">${field.label}：</span>
                    <span class="faker-info-value">${value}</span>
                    <button class="faker-copy-item-btn" data-value="${value}">复制</button>
                `;
                resultItems.appendChild(itemDiv);
            }
        });
        
        // 为每个复制按钮添加事件监听器
        document.querySelectorAll('.faker-copy-item-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const value = e.target.getAttribute('data-value');
                copyToClipboard(value);
            });
        });
        
        // 显示结果容器
        resultContainer.style.display = 'block';
    }
    
    // 复制到剪贴板
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // 显示成功消息
            successMessage.style.opacity = '1';
            setTimeout(() => {
                successMessage.style.opacity = '0';
            }, 2000);
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制');
        });
    }
    
    // 生成个人信息的主函数
    async function generatePersonalInfo() {
        setButtonLoading(true);
        
        try {
            // 直接使用API生成数据
            const info = await generateFromAPI();
            displayInfo(info);
        } catch (error) {
            console.error('生成数据失败:', error);
            showStatusMessage('生成数据失败: ' + error.message, 'error');
        } finally {
            setButtonLoading(false);
        }
    }
    
    // 添加事件监听器
    generateBtn.addEventListener('click', generatePersonalInfo);
    
    copyAllBtn.addEventListener('click', () => {
        // 收集所有信息
        const allText = Array.from(document.querySelectorAll('.faker-info-item')).map(item => {
            const label = item.querySelector('.faker-info-label').textContent;
            const value = item.querySelector('.faker-info-value').textContent;
            return `${label}${value}`;
        }).join('\n');
        
        copyToClipboard(allText);
    });
    
    closeBtn.addEventListener('click', () => {
        resultContainer.style.display = 'none';
    });
})();

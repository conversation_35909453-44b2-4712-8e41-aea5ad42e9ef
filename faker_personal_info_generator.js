// ==UserScript==
// @name         个人信息生成器
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  使用Faker.js生成个人信息，包括姓名、用户名、密码、邮箱、地址等
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @require      https://cdn.jsdelivr.net/npm/@faker-js/faker@8.0.2/dist/cdn/faker.min.js
// ==/UserScript==

(function() {
    'use strict';

    // 创建UI样式
    const style = document.createElement('style');
    style.textContent = `
        #faker-info-generator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 10000;
            font-family: Arial, sans-serif;
        }
        
        #faker-generate-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        
        #faker-generate-btn:hover {
            background-color: #45a049;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        #faker-result-panel {
            position: fixed;
            bottom: 70px;
            right: 20px;
            width: 350px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 15px;
            display: none;
            z-index: 10000;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        #faker-result-panel h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        
        .faker-info-item {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .faker-copy-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            margin-top: 10px;
            transition: all 0.3s;
        }
        
        .faker-copy-btn:hover {
            background-color: #0b7dda;
        }
        
        .faker-close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #999;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }
        
        .faker-close-btn:hover {
            color: #666;
        }
        
        .faker-success-message {
            position: fixed;
            bottom: 120px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s;
        }
    `;
    document.head.appendChild(style);

    // 创建生成按钮
    const generateBtn = document.createElement('button');
    generateBtn.id = 'faker-generate-btn';
    generateBtn.textContent = '生成个人信息';
    
    // 创建结果面板
    const resultPanel = document.createElement('div');
    resultPanel.id = 'faker-result-panel';
    
    // 创建容器
    const container = document.createElement('div');
    container.id = 'faker-info-generator';
    container.appendChild(generateBtn);
    container.appendChild(resultPanel);
    
    // 添加到页面
    document.body.appendChild(container);
    
    // 创建成功消息元素
    const successMessage = document.createElement('div');
    successMessage.className = 'faker-success-message';
    successMessage.textContent = '已复制到剪贴板';
    successMessage.style.opacity = '0';
    document.body.appendChild(successMessage);

    // 生成随机密码
    function generatePassword(length = 10) {
        const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
        let password = '';
        for (let i = 0; i < length; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
    }

    // 生成个人信息
    function generatePersonalInfo() {
        // 使用中文区域设置
        const faker = window.faker;
        faker.locale = 'zh_CN';
        
        // 生成基本信息
        const firstName = faker.person.firstName();
        const lastName = faker.person.lastName();
        const fullName = lastName + firstName;
        const username = pinyin(fullName) + Math.floor(Math.random() * 100);
        const password = generatePassword(12);
        const email = username + '@' + faker.internet.domainName();
        const streetAddress = faker.location.streetAddress();
        const city = faker.location.city();
        const state = faker.location.state();
        const zipCode = faker.location.zipCode();
        
        return {
            fullName,
            username,
            password,
            email,
            streetAddress,
            city,
            state,
            zipCode
        };
    }
    
    // 简单的拼音转换函数（仅作示例，不支持所有汉字）
    function pinyin(chinese) {
        // 这只是一个简化版，实际应用中应使用完整的拼音库
        const pinyinMap = {
            '李': 'li', '王': 'wang', '张': 'zhang', '刘': 'liu', '陈': 'chen',
            '杨': 'yang', '赵': 'zhao', '黄': 'huang', '周': 'zhou', '吴': 'wu',
            '徐': 'xu', '孙': 'sun', '胡': 'hu', '朱': 'zhu', '高': 'gao',
            '林': 'lin', '何': 'he', '郭': 'guo', '马': 'ma', '罗': 'luo',
            '梁': 'liang', '宋': 'song', '郑': 'zheng', '谢': 'xie', '韩': 'han',
            '唐': 'tang', '冯': 'feng', '于': 'yu', '董': 'dong', '萧': 'xiao',
            '程': 'cheng', '曹': 'cao', '袁': 'yuan', '邓': 'deng', '许': 'xu',
            '傅': 'fu', '沈': 'shen', '曾': 'zeng', '彭': 'peng', '吕': 'lv',
            '苏': 'su', '卢': 'lu', '蒋': 'jiang', '蔡': 'cai', '贾': 'jia',
            '丁': 'ding', '魏': 'wei', '薛': 'xue', '叶': 'ye', '阎': 'yan',
            '余': 'yu', '潘': 'pan', '杜': 'du', '戴': 'dai', '夏': 'xia',
            '钟': 'zhong', '汪': 'wang', '田': 'tian', '任': 'ren', '姜': 'jiang',
            '范': 'fan', '方': 'fang', '石': 'shi', '姚': 'yao', '谭': 'tan',
            '廖': 'liao', '邹': 'zou', '熊': 'xiong', '金': 'jin', '陆': 'lu',
            '郝': 'hao', '孔': 'kong', '白': 'bai', '崔': 'cui', '康': 'kang',
            '毛': 'mao', '邱': 'qiu', '秦': 'qin', '江': 'jiang', '史': 'shi',
            '顾': 'gu', '侯': 'hou', '邵': 'shao', '孟': 'meng', '龙': 'long',
            '万': 'wan', '段': 'duan', '雷': 'lei', '钱': 'qian', '汤': 'tang',
            '尹': 'yin', '黎': 'li', '易': 'yi', '常': 'chang', '武': 'wu',
            '乔': 'qiao', '贺': 'he', '赖': 'lai', '龚': 'gong', '文': 'wen',
            '明': 'ming', '华': 'hua', '平': 'ping', '静': 'jing', '丽': 'li',
            '强': 'qiang', '伟': 'wei', '敏': 'min', '艳': 'yan', '超': 'chao',
            '勇': 'yong', '娜': 'na', '艺': 'yi', '颖': 'ying', '琴': 'qin',
            '云': 'yun', '建': 'jian', '洁': 'jie', '琳': 'lin', '兰': 'lan',
            '凤': 'feng', '洋': 'yang', '海': 'hai', '燕': 'yan', '晶': 'jing',
            '冰': 'bing', '瑶': 'yao', '红': 'hong', '梅': 'mei', '岩': 'yan'
        };
        
        let result = '';
        for (let i = 0; i < chinese.length; i++) {
            const char = chinese.charAt(i);
            result += pinyinMap[char] || char;
        }
        return result;
    }

    // 显示生成的信息
    function displayInfo(info) {
        resultPanel.innerHTML = `
            <h3>生成的个人信息</h3>
            <div class="faker-info-item">姓名：${info.fullName}</div>
            <div class="faker-info-item">用户名：${info.username}</div>
            <div class="faker-info-item">密码：${info.password}</div>
            <div class="faker-info-item">邮箱：${info.email}</div>
            <div class="faker-info-item">地址：${info.streetAddress}</div>
            <div class="faker-info-item">城市：${info.city}</div>
            <div class="faker-info-item">州/省：${info.state}</div>
            <div class="faker-info-item">邮编：${info.zipCode}</div>
            <button class="faker-copy-btn" id="faker-copy-formatted">复制格式化信息</button>
            <button class="faker-copy-btn" id="faker-copy-all" style="margin-left: 10px;">复制所有信息</button>
            <button class="faker-close-btn">×</button>
        `;
        
        resultPanel.style.display = 'block';
        
        // 添加复制按钮事件
        document.getElementById('faker-copy-formatted').addEventListener('click', () => {
            const formattedText = `姓名：${info.fullName}
用户名：${info.username}
密码：${info.password}
邮箱：${info.email}
地址：${info.streetAddress}
城市：${info.city}
州/省：${info.state}
邮编：${info.zipCode}`;
            
            copyToClipboard(formattedText);
        });
        
        document.getElementById('faker-copy-all').addEventListener('click', () => {
            const allText = `${info.fullName}
${info.username}
${info.password}
${info.email}
${info.streetAddress}
${info.city}
${info.state}
${info.zipCode}`;
            
            copyToClipboard(allText);
        });
        
        // 添加关闭按钮事件
        resultPanel.querySelector('.faker-close-btn').addEventListener('click', () => {
            resultPanel.style.display = 'none';
        });
    }
    
    // 复制到剪贴板
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // 显示成功消息
            successMessage.style.opacity = '1';
            setTimeout(() => {
                successMessage.style.opacity = '0';
            }, 2000);
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制');
        });
    }

    // 添加生成按钮点击事件
    generateBtn.addEventListener('click', () => {
        const info = generatePersonalInfo();
        displayInfo(info);
    });
})();
